/** @type {import('next').NextConfig} */
const nextConfig = {
  // Optimized for Heroku deployment
  trailingSlash: false,
  poweredByHeader: false,
  compress: true,

  // Image optimization
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'adukingtest-f5dabea069f8.herokuapp.com',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
      },
      {
        protocol: 'https',
        hostname: 'firebasestorage.googleapis.com',
      },
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
    formats: ['image/webp', 'image/avif'],
  },

  // Experimental features for better performance
  experimental: {
    // Remove optimizeCss as it's causing build issues
  },

  // Webpack configuration for better bundling
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // Don't resolve 'fs' module on the client to prevent this error on build
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }
    return config;
  },

  // Environment variables - these are automatically available in the browser
  // No need to explicitly define them here as Next.js handles NEXT_PUBLIC_ vars automatically
}

module.exports = nextConfig
