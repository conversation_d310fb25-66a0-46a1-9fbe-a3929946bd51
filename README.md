# Adukrom Kingdom Website

Official website for the Adukrom Kingdom, built with Next.js and Firebase.

## Getting Started

### Prerequisites

- Node.js 16.8 or later
- npm or yarn
- Firebase project with Firestore and Storage enabled

### Environment Setup

1. Copy the example environment file:
   ```bash
   cp .env.example .env.local
   ```

2. Update the `.env.local` file with your Firebase project credentials.

### Installation

1. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

2. Run the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

3. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Deployment

### Vercel (Recommended)

1. Push your code to a GitHub/GitLab/Bitbucket repository.
2. Import the repository on Vercel.
3. Add the following environment variables in Vercel's project settings:
   - `NEXT_PUBLIC_FIREBASE_API_KEY`
   - `NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN`
   - `NEXT_PUBLIC_FIREBASE_PROJECT_ID`
   - `NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET`
   - `NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID`
   - `NEXT_PUBLIC_FIREBASE_APP_ID`
   - `NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID` (optional)

### Static Export

1. Build the static files:
   ```bash
   npm run build
   ```

2. The static files will be in the `out` directory.

## Firebase Configuration

Make sure your Firebase project has the following services enabled:

- Authentication (if using)
- Firestore Database
- Storage
- Hosting (if deploying to Firebase Hosting)

## Troubleshooting

### Missing Environment Variables

If you see errors about missing Firebase environment variables, make sure:
1. The `.env.local` file exists and is properly formatted
2. All required variables are present
3. The variables match your Firebase project settings

### Image Loading Issues

If images are not loading:
1. Check that the file paths in your code match the actual file locations
2. Ensure the files exist in the `public` directory
3. Verify file permissions

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
