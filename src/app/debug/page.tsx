'use client';

import { useEffect, useState } from 'react';
import { ref, listAll, getDownloadURL } from 'firebase/storage';
import { storage } from '@/lib/firebase';

export default function DebugPage() {
  const [envVars, setEnvVars] = useState<Record<string, string>>({});
  const [storageTest, setStorageTest] = useState<{
    status: 'idle' | 'loading' | 'success' | 'error';
    message: string;
    files: Array<{ name: string; url: string }>;
    error?: any;
  }>({ status: 'idle', message: '', files: [] });

  // Firebase configuration is now centralized in @/lib/firebase

  useEffect(() => {
    // Set environment variables for display
    setEnvVars({
      'NEXT_PUBLIC_FIREBASE_API_KEY': process.env.NEXT_PUBLIC_FIREBASE_API_KEY?.substring(0, 10) + '...' || 'NOT FOUND',
      'NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN': process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || 'NOT FOUND',
      'NEXT_PUBLIC_FIREBASE_PROJECT_ID': process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'NOT FOUND',
      'NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET': process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || 'NOT FOUND',
      'NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID': process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || 'NOT FOUND',
      'NEXT_PUBLIC_FIREBASE_APP_ID': process.env.NEXT_PUBLIC_FIREBASE_APP_ID?.substring(0, 10) + '...' || 'NOT FOUND',
      'NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID': process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID || 'NOT FOUND',
    });
  }, []);

  const testStorageAccess = async () => {
    try {
      setStorageTest(prev => ({ ...prev, status: 'loading', message: 'Initializing Firebase...' }));
      
      if (!storage) {
        throw new Error('Firebase Storage not available');
      }
      
      setStorageTest(prev => ({ ...prev, message: 'Using centralized Firebase. Listing files in "Website Images" folder...' }));
      
      // List files in the Website Images folder
      const folderRef = ref(storage, 'Website Images');
      const result = await listAll(folderRef);
      
      setStorageTest(prev => ({ 
        ...prev, 
        message: `Found ${result.items.length} items in "Website Images" folder. Getting download URLs...` 
      }));
      
      // Get download URLs for each file
      const files = await Promise.all(
        result.items.map(async (item) => {
          const url = await getDownloadURL(item);
          return { name: item.name, url };
        })
      );
      
      setStorageTest({
        status: 'success',
        message: `Successfully accessed ${files.length} files in "Website Images" folder.`,
        files,
      });
      
    } catch (error: any) {
      console.error('Storage test error:', error);
      setStorageTest({
        status: 'error',
        message: `Error: ${error?.message || 'Unknown error'}`,
        files: [],
        error: {
          code: error?.code,
          message: error?.message,
          serverResponse: error?.serverResponse,
          name: error?.name,
          stack: error?.stack,
        },
      });
    }
  };

  return (
    <div style={{ padding: '2rem', fontFamily: 'sans-serif', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>Firebase Debug Page</h1>
      
      <section style={{ marginBottom: '3rem' }}>
        <h2>Environment Variables</h2>
        <div style={{ 
          marginTop: '1rem', 
          padding: '1rem', 
          backgroundColor: '#f5f5f5', 
          borderRadius: '4px',
          overflowX: 'auto'
        }}>
          <pre style={{ margin: 0 }}>{JSON.stringify(envVars, null, 2)}</pre>
        </div>
      </section>

      <section style={{ marginBottom: '3rem' }}>
        <h2>Storage Test</h2>
        <div style={{ 
          marginTop: '1rem', 
          padding: '1.5rem', 
          backgroundColor: storageTest.status === 'error' ? '#ffebee' : 
                          storageTest.status === 'success' ? '#e8f5e9' : '#e3f2fd',
          borderRadius: '4px',
          borderLeft: `4px solid ${
            storageTest.status === 'error' ? '#f44336' : 
            storageTest.status === 'success' ? '#4caf50' : '#2196f3'
          }`,
        }}>
          <div style={{ marginBottom: '1rem' }}>
            <button 
              onClick={testStorageAccess}
              disabled={storageTest.status === 'loading'}
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: '#1976d2',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '1rem',
                opacity: storageTest.status === 'loading' ? 0.7 : 1,
              }}
            >
              {storageTest.status === 'loading' ? 'Testing...' : 'Test Storage Access'}
            </button>
          </div>
          
          <div style={{ marginBottom: '1rem' }}>
            <h3 style={{ margin: '0 0 0.5rem 0' }}>Status:</h3>
            <div style={{ 
              padding: '0.5rem', 
              backgroundColor: 'white', 
              borderRadius: '4px',
              fontFamily: 'monospace',
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word'
            }}>
              {storageTest.status === 'idle' ? 'Click the button to test storage access' : storageTest.message}
            </div>
          </div>
          
          {storageTest.status === 'success' && (
            <div>
              <h3>Files in "Website Images" folder:</h3>
              <div style={{ 
                display: 'grid', 
                gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))', 
                gap: '1rem',
                marginTop: '1rem'
              }}>
                {storageTest.files.map((file, index) => (
                  <div 
                    key={index} 
                    style={{ 
                      border: '1px solid #ddd', 
                      borderRadius: '4px', 
                      padding: '0.5rem',
                      backgroundColor: 'white'
                    }}
                  >
                    <div style={{ 
                      height: '150px', 
                      backgroundColor: '#f5f5f5', 
                      display: 'flex', 
                      alignItems: 'center', 
                      justifyContent: 'center',
                      marginBottom: '0.5rem',
                      overflow: 'hidden'
                    }}>
                      <img 
                        src={file.url} 
                        alt={file.name} 
                        style={{ 
                          maxWidth: '100%', 
                          maxHeight: '100%',
                          objectFit: 'contain'
                        }} 
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.onerror = null;
                          target.src = 'data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%22200%22%20height%3D%22200%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20200%20200%22%20preserveAspectRatio%3D%22none%22%3E%3Cdefs%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E%23holder_18e9c7a5a0b%20text%20%7B%20fill%3A%23AAAAAA%3Bfont-weight%3Abold%3Bfont-family%3AArial%2C%20Helvetica%2C%20Open%20Sans%2C%20sans-serif%2C%20monospace%3Bfont-size%3A10pt%20%7D%20%3C%2Fstyle%3E%3C%2Fdefs%3E%3Cg%20id%3D%22holder_18e9c7a5a0b%22%3E%3Crect%20width%3D%22200%22%20height%3D%22200%22%20fill%3D%22%23F5F5F5%22%3E%3C%2Frect%3E%3Cg%3E%3Ctext%20x%3D%2274.0859375%22%20y%3D%22104.5%22%3EImage%3C%2Ftext%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E';
                        }}
                      />
                    </div>
                    <div style={{ 
                      fontSize: '0.8rem', 
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      textAlign: 'center'
                    }}>
                      {file.name}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {storageTest.status === 'error' && (
            <div>
              <h3>Error Details:</h3>
              <div style={{ 
                padding: '1rem', 
                backgroundColor: 'white', 
                borderRadius: '4px',
                fontFamily: 'monospace',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
                maxHeight: '300px',
                overflowY: 'auto'
              }}>
                {JSON.stringify(storageTest.error, null, 2)}
              </div>
            </div>
          )}
        </div>
      </section>

      <section>
        <h2>Next Steps:</h2>
        <ol style={{ lineHeight: '1.6' }}>
          <li><strong>Check Environment Variables</strong> - Ensure all Firebase environment variables are correctly set (not 'NOT FOUND')</li>
          <li><strong>Test Storage Access</strong> - Use the button above to test access to Firebase Storage</li>
          <li><strong>Check Console</strong> - Open browser developer tools (F12) and check the Console tab for detailed error messages</li>
          <li><strong>Verify Storage Rules</strong> - In the Firebase Console, go to Storage &gt; Rules and ensure read access is allowed</li>
          <li><strong>Check Folder Name</strong> - Ensure the folder name in Storage matches exactly (case-sensitive)</li>
          <li><strong>Restart Server</strong> - If you updated environment variables, restart your Next.js development server</li>
        </ol>
      </section>
    </div>
  );
}
