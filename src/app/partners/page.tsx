'use client';

import React, { useState, useEffect } from 'react';
import { FaHandshake, FaEnvelope, FaExternalLinkAlt, FaSpinner, FaGlobe, FaUsers, FaRocket, FaHeart } from 'react-icons/fa';
import Link from 'next/link';
import Header from '../../components/layout/Header';
import Footer from '../../components/layout/Footer';

interface Partner {
  id: string;
  name: string;
  description: string;
  category: string;
  website: string;
  logo: string;
  logoUrl?: string;
}

// Strategic Partners data matching the reference design
const strategicPartners: Partner[] = [
  {
    id: 'remit-global',
    name: 'Remit Global',
    description: 'Strategic financial partner supporting the Kingdom\'s economic initiatives and international remittance services.',
    logo: '/Website Images/remit-global-logo.png',
    website: 'https://remitglobalinc.com/',
    category: 'Financial Services'
  },
  {
    id: 'royal-lion',
    name: 'Royal Lion',
    description: 'Heritage and cultural partner preserving the Kingdom\'s traditions and promoting African cultural heritage.',
    logo: '/Website Images/royal_lion_logo.png',
    website: 'https://queenmotherlatonja.com/',
    category: 'Cultural Heritage'
  },
  {
    id: 'tef',
    name: 'The Ellison Foundation',
    description: 'Educational development partner advancing knowledge, skills, and opportunities in the community.',
    logo: '/Website Images/tef-logo2-transparent.png',
    website: 'https://ellisonoutreachfoundation.com/',
    category: 'Education'
  },
  {
    id: 'lightace',
    name: 'LightAce Global',
    description: 'Innovation and technology partner bringing cutting-edge digital solutions to the Kingdom.',
    logo: '/Website Images/lightace-global-logo.png',
    website: 'https://lightaceglobal.wixsite.com/lightaceglobal',
    category: 'Technology'
  },
  {
    id: 'akuapem',
    name: 'Akuapem Nifaman Council',
    description: 'Traditional governance partner supporting the Kingdom\'s leadership structure and cultural authority.',
    logo: '/Website Images/akuaapem_nifaman_council_logo.png',
    website: 'https://www.akuapemnifamancouncil.com',
    category: 'Governance'
  }
];

export default function PartnersPage() {

  return (
    <main className="min-h-screen">
      <Header />

      {/* Hero Section - Connected to header */}
      <section className="relative pt-20 pb-20 px-4 overflow-hidden bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90">
        {/* Background gradients */}
        <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/20 via-royalGold/5 to-royalGold/15"></div>
        <div className="absolute inset-0 bg-gradient-to-bl from-transparent via-royalGold/3 to-royalGold/8"></div>

        {/* Animated background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="royal-pattern w-full h-full"></div>
        </div>

        <div className="container mx-auto relative z-10 text-center pt-12">
          <h1 className="text-5xl md:text-7xl font-serif font-bold text-white mb-6">
            Strategic Partners
          </h1>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-8"></div>
          <p className="text-xl text-white/90 max-w-4xl mx-auto leading-relaxed mb-12">
            At the heart of the Kingdom's vision for prosperity, unity and global impact are our Strategic Partners of the Crown.
            Together, we build bridges across industries, cultures, and continents.
          </p>

          {/* Partnership Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto mb-12">
            <div className="text-center">
              <div className="text-4xl font-bold text-royalGold mb-2">5+</div>
              <p className="text-white/80 text-sm">Strategic Partners</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-royalGold mb-2">10+</div>
              <p className="text-white/80 text-sm">Countries Reached</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-royalGold mb-2">50K+</div>
              <p className="text-white/80 text-sm">Lives Impacted</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-royalGold mb-2">100%</div>
              <p className="text-white/80 text-sm">Commitment</p>
            </div>
          </div>
        </div>
      </section>

      {/* Partners Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-serif font-bold text-royalBlue mb-4">
              Our Strategic Partners
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
            <p className="text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Each partnership represents a shared commitment to excellence, innovation, and positive impact.
              Together, we're building a stronger, more connected future.
            </p>
          </div>

          {/* Partners Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12 max-w-6xl mx-auto">
            {strategicPartners.map((partner, index) => (
              <div
                key={partner.id}
                className="group bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-royalGold/30 overflow-hidden"
              >
                <div className="p-8 text-center">
                  {/* Logo Circle */}
                  <div className="w-24 h-24 mx-auto mb-6 bg-white rounded-full flex items-center justify-center border-4 border-royalGold/20 group-hover:border-royalGold/50 transition-all duration-300 overflow-hidden shadow-lg">
                    <img
                      src={partner.logo}
                      alt={partner.name}
                      className="w-full h-full object-contain p-3"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = document.createElement('div');
                        fallback.className = 'w-full h-full bg-gradient-to-br from-royalGold to-yellow-500 rounded-full flex items-center justify-center text-2xl text-white font-bold';
                        fallback.textContent = partner.name.charAt(0);
                        target.parentNode?.insertBefore(fallback, target);
                      }}
                    />
                  </div>

                  {/* Partner Info */}
                  <h3 className="text-xl font-bold text-royalBlue mb-2 group-hover:text-royalGold transition-colors">
                    {partner.name}
                  </h3>

                  <div className="text-sm text-royalGold/80 font-medium mb-4 bg-royalGold/10 px-3 py-1 rounded-full inline-block">
                    {partner.category}
                  </div>

                  <p className="text-gray-600 text-sm leading-relaxed mb-6">
                    {partner.description}
                  </p>

                  {/* Visit Website Link */}
                  <a
                    href={partner.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-royalGold hover:text-royalBlue font-semibold text-sm transition-colors duration-300 group-hover:scale-105 transform"
                  >
                    Visit Website
                    <FaExternalLinkAlt className="ml-2 text-xs" />
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Partnership Benefits Section */}
      <section className="py-20 bg-gradient-to-br from-cream via-ivory to-cream">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-serif font-bold text-royalBlue mb-4">
              Partnership Benefits
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
            <p className="text-gray-600 max-w-3xl mx-auto leading-relaxed">
              When you partner with Adukrom Kingdom, you join a network of excellence that opens doors to new opportunities and meaningful impact.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            <div className="text-center group">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-royalGold to-yellow-500 rounded-full flex items-center justify-center text-white text-2xl group-hover:scale-110 transition-transform duration-300">
                <FaGlobe />
              </div>
              <h3 className="text-xl font-bold text-royalBlue mb-2">Global Reach</h3>
              <p className="text-gray-600 text-sm">Access international markets and expand your global presence through our network.</p>
            </div>

            <div className="text-center group">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-royalGold to-yellow-500 rounded-full flex items-center justify-center text-white text-2xl group-hover:scale-110 transition-transform duration-300">
                <FaUsers />
              </div>
              <h3 className="text-xl font-bold text-royalBlue mb-2">Community Impact</h3>
              <p className="text-gray-600 text-sm">Make a meaningful difference in communities while growing your business.</p>
            </div>

            <div className="text-center group">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-royalGold to-yellow-500 rounded-full flex items-center justify-center text-white text-2xl group-hover:scale-110 transition-transform duration-300">
                <FaRocket />
              </div>
              <h3 className="text-xl font-bold text-royalBlue mb-2">Innovation</h3>
              <p className="text-gray-600 text-sm">Collaborate on cutting-edge projects and innovative solutions.</p>
            </div>

            <div className="text-center group">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-royalGold to-yellow-500 rounded-full flex items-center justify-center text-white text-2xl group-hover:scale-110 transition-transform duration-300">
                <FaHeart />
              </div>
              <h3 className="text-xl font-bold text-royalBlue mb-2">Shared Values</h3>
              <p className="text-gray-600 text-sm">Partner with organizations that share your commitment to excellence.</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90">
        <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/20 via-royalGold/5 to-royalGold/15"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-6">
              Become a Strategic Partner
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-8"></div>
            <p className="text-xl text-white/90 mb-12 leading-relaxed">
              Join our exclusive network of strategic partners and be part of a movement that's shaping the future.
              Together, we can achieve extraordinary things.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link
                href="/contact"
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-royalGold to-yellow-500 text-royalBlue font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 transform"
              >
                <FaHandshake className="mr-2" />
                Partner With Us
              </Link>
              <Link
                href="/contact"
                className="inline-flex items-center px-8 py-4 bg-transparent border-2 border-white/30 text-white font-bold rounded-xl hover:bg-white/10 transition-all duration-300"
              >
                <FaEnvelope className="mr-2" />
                Get in Touch
              </Link>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  );
}
