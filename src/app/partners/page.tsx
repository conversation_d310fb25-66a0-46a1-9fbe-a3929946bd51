'use client';

import React, { useState, useEffect } from 'react';
import { FaExternalLinkAlt, FaSpinner } from 'react-icons/fa';
import Link from 'next/link';
import Header from '../../components/layout/Header';
import Footer from '../../components/layout/Footer';
import { getPartners } from '@/lib/firebase';

interface Partner {
  id: string;
  name: string;
  description: string;
  category: string;
  website: string;
  logo: string;
  logoUrl?: string;
  featured?: boolean;
}

// Featured partners matching the reference site
const featuredPartners: Partner[] = [
  {
    id: 'remit-global',
    name: 'Remit Global',
    description: 'Strategic financial partner supporting the Kingdom\'s economic initiatives.',
    logo: '/Website Images/remit-global-logo.png',
    website: 'https://remitglobalinc.com/',
    category: 'Strategic Financial Partners',
    featured: true
  },
  {
    id: 'royal-lion',
    name: 'Royal Lion',
    description: 'Heritage and cultural partner preserving the Kingdom\'s traditions.',
    logo: '/Website Images/royal_lion_logo.png',
    website: 'https://queenmotherlatonja.com/',
    category: 'Heritage & Cultural Partners',
    featured: true
  },
  {
    id: 'tef',
    name: 'TEF',
    description: 'Educational development partner advancing knowledge and skills in the community.',
    logo: '/Website Images/tef-logo2-transparent.png',
    website: 'https://ellisonoutreachfoundation.com/',
    category: 'Educational Development Partners',
    featured: true
  },
  {
    id: 'lightace',
    name: 'Lightace Global',
    description: 'Innovation and technology partner bringing digital solutions to the Kingdom.',
    logo: '/Website Images/lightace-global-logo.png',
    website: 'https://lightaceglobal.wixsite.com/lightaceglobal',
    category: 'Innovation & Technology Partners',
    featured: true
  },
  {
    id: 'akuapem',
    name: 'Akuapem Nifaman Council',
    description: 'Traditional governance partner supporting the Kingdom\'s leadership structure.',
    logo: '/Website Images/akuaapem_nifaman_council_logo.png',
    website: 'https://www.akuapemnifamancouncil.com',
    category: 'Strategic Financial Partners',
    featured: true
  }
];

export default function PartnersPage() {
  const [allPartners, setAllPartners] = useState<Partner[]>(featuredPartners);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPartners = async () => {
      try {
        setLoading(true);
        const partnersData = await getPartners();

        if (partnersData && partnersData.length > 0) {
          // Map Firebase partners to our interface
          const mappedPartners: Partner[] = partnersData.map(partner => ({
            id: partner.id,
            name: partner.name || 'Partner',
            description: partner.description || 'Strategic partner',
            category: partner.category || 'General',
            website: partner.website || '#',
            logo: partner.logo || '/Website Images/logo.png',
            logoUrl: partner.logo,
            featured: featuredPartners.some(fp => fp.id === partner.id)
          }));
          setAllPartners(mappedPartners);
        } else {
          setAllPartners(featuredPartners);
        }
      } catch (err) {
        console.error('Error fetching partners:', err);
        setAllPartners(featuredPartners);
      } finally {
        setLoading(false);
      }
    };

    fetchPartners();
  }, []);

  // Group partners by category
  const partnersByCategory = allPartners.reduce((acc, partner) => {
    if (!acc[partner.category]) {
      acc[partner.category] = [];
    }
    acc[partner.category].push(partner);
    return acc;
  }, {} as Record<string, Partner[]>);

  if (loading) {
    return (
      <main className="min-h-screen">
        <Header />
        <div className="pt-20 min-h-[60vh] flex items-center justify-center">
          <div className="flex flex-col items-center">
            <FaSpinner className="animate-spin text-4xl text-royalGold mb-4" />
            <p className="text-gray-600">Loading partners...</p>
          </div>
        </div>
        <Footer />
      </main>
    );
  }

  return (
    <main className="min-h-screen">
      <Header />

      {/* Hero Section */}
      <section className="relative pt-20 pb-16 px-4 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90">
        <div className="container mx-auto text-center pt-12">
          <h1 className="text-5xl md:text-6xl font-serif font-bold text-white mb-6">
            Strategic Partners
          </h1>
          <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
            Meet the organizations and institutions working with the Kingdom of Adukrom to build a brighter future for Ghana.
          </p>
        </div>
      </section>

      {/* Featured Partners Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Featured Partners</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {featuredPartners.map((partner) => (
              <div key={partner.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
                <div className="aspect-video relative bg-gray-100">
                  <img
                    src={partner.logo}
                    alt={partner.name}
                    className="w-full h-full object-contain p-4"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-royalGold text-white text-xs font-bold px-2 py-1 rounded">
                      Featured
                    </span>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{partner.name}</h3>
                  <p className="text-gray-600 text-sm mb-4">{partner.description}</p>
                  <a
                    href={partner.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-royalGold hover:text-royalBlue font-medium text-sm transition-colors"
                  >
                    Visit Website <FaExternalLinkAlt className="ml-1 text-xs" />
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Partners by Category Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Our Partners By Category</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              We collaborate with a diverse range of organizations across various sectors to achieve our mission.
            </p>
          </div>

          {Object.entries(partnersByCategory).map(([category, partners]) => (
            <div key={category} className="mb-16">
              <h3 className="text-2xl font-bold text-gray-900 mb-8">{category}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {partners.map((partner) => (
                  <div key={partner.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
                    <div className="aspect-video relative bg-gray-100">
                      <img
                        src={partner.logo}
                        alt={partner.name}
                        className="w-full h-full object-contain p-4"
                      />
                      {partner.featured && (
                        <div className="absolute top-4 left-4">
                          <span className="bg-royalGold text-white text-xs font-bold px-2 py-1 rounded">
                            Featured
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="p-6">
                      <h4 className="text-xl font-bold text-gray-900 mb-2">{partner.name}</h4>
                      <p className="text-gray-600 text-sm mb-4">{partner.description}</p>
                      <a
                        href={partner.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-royalGold hover:text-royalBlue font-medium text-sm transition-colors"
                      >
                        Visit Website <FaExternalLinkAlt className="ml-1 text-xs" />
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Become a Strategic Partner Section */}
      <section className="py-16 bg-royalBlue">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">Become a Strategic Partner</h2>
          <p className="text-white/90 max-w-2xl mx-auto mb-8">
            Interested in partnering with the Kingdom of Adukrom? We welcome collaborations with organizations
            that share our vision for development, cultural preservation, and community empowerment.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="inline-flex items-center px-6 py-3 bg-royalGold text-royalBlue font-bold rounded-lg hover:bg-yellow-400 transition-colors"
            >
              Contact Us
            </Link>
            <Link
              href="/"
              className="inline-flex items-center px-6 py-3 bg-transparent border-2 border-white text-white font-bold rounded-lg hover:bg-white/10 transition-colors"
            >
              Learn More About Us
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  );
}
