// Firebase imports
import {
  collection,
  getDocs,
  onSnapshot,
  QuerySnapshot,
  DocumentSnapshot,
  DocumentData,
  Timestamp,
  Unsubscribe
} from 'firebase/firestore';
import {
  ref,
  getDownloadURL,
  listAll
} from 'firebase/storage';
import {
  getAnalytics,
  isSupported,
  logEvent,
  setUserId,
  setUserProperties
} from 'firebase/analytics';
import {
  getAuth,
  onAuthStateChanged
} from 'firebase/auth';

// Remove duplicate import - we'll use our own initialized instances

// Initialize auth lazily on client side only
let authInstance: any = null;
export const getAuthInstance = () => {
  if (typeof window === 'undefined') {
    return null;
  }
  if (!authInstance) {
    const { app } = ensureFirebaseInitialized();
    if (app) {
      authInstance = getAuth(app);
    }
  }
  return authInstance;
};

// Export auth getter for backward compatibility
export const auth = new Proxy({} as any, {
  get(_target, prop) {
    const authInst = getAuthInstance();
    return authInst ? authInst[prop] : undefined;
  }
});

// Initialize analytics
export let analytics: any;

// Initialize analytics on client side only
if (typeof window !== 'undefined') {
  // Delay analytics initialization to avoid blocking
  setTimeout(() => {
    isSupported().then(supported => {
      if (supported) {
        const { app } = ensureFirebaseInitialized();
        if (app) {
          analytics = getAnalytics(app);
          // Log page view
          logEvent(analytics, 'page_view', {
            page_title: document.title,
            page_location: window.location.href,
            page_path: window.location.pathname,
            referrer: document.referrer || 'direct'
          });
        }
      }
    }).catch(error => {
      console.warn('Analytics initialization failed:', error);
    });
  }, 1000);
}

// Type Definitions
export interface GalleryImage {
  id: string;
  url: string;
  title?: string;
  description?: string;
  category?: string;
  uploadedAt?: Timestamp | Date;
  fullUrl?: string;
  path?: string;
  [key: string]: any; // For any additional fields
}

export interface NewsArticle {
  id: string;
  title: string;
  excerpt: string;
  content?: string;
  imageUrl?: string;
  category: string;
  date: Timestamp | Date;
  readTime?: string;
  author?: string;
  isFeatured?: boolean;
  [key: string]: any; // For any additional fields
}

export interface Partner {
  id: string;
  name: string;
  description: string;
  logo?: string;
  website?: string;
  category: string;
  [key: string]: any; // For any additional fields
}

type ImageType = 'gallery' | 'partner' | 'news' | 'other';

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

// Firebase is now initialized in ./config.ts and ./client.ts
// We import the initialized instances from there

// Set user properties when user is signed in - initialize lazily
if (isBrowser) {
  // Delay auth state listener setup until Firebase is initialized
  setTimeout(() => {
    try {
      const authInst = getAuthInstance();
      if (authInst) {
        onAuthStateChanged(authInst, (user) => {
          if (user && analytics) {
            setUserId(analytics, user.uid);
            setUserProperties(analytics, {
              sign_up_method: user.providerData[0]?.providerId || 'unknown',
              email_verified: user.emailVerified ? 'verified' : 'unverified'
            });
          }
        });
      }
    } catch (error) {
      console.warn('Auth state listener setup failed:', error);
    }
  }, 1500);
}

// Only validate in browser environment and in production
if (isBrowser && process.env.NODE_ENV === 'production') {
  const requiredEnvVars = [
    'NEXT_PUBLIC_FIREBASE_API_KEY',
    'NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN',
    'NEXT_PUBLIC_FIREBASE_PROJECT_ID',
    'NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET',
    'NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID',
    'NEXT_PUBLIC_FIREBASE_APP_ID'
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.warn('Missing required Firebase environment variables in production:', missingVars);
    // Don't throw in development to allow the app to run with missing vars
    if (process.env.NODE_ENV === 'production') {
      throw new Error(`Missing required Firebase configuration: ${missingVars.join(', ')}`);
    }
  }
}

// Firebase configuration is now handled in firebase-client.ts

// Import client-side Firebase initialization
import { getFirebaseInstances } from './firebase-client';

// Ensure Firebase is initialized before any operations
const ensureFirebaseInitialized = () => {
  if (typeof window === 'undefined') {
    return { app: null, db: null, storage: null };
  }
  return getFirebaseInstances();
};

// Get Firebase instances (will be null on server side)
const getFirebaseServices = () => {
  return ensureFirebaseInitialized();
};

// Function to get a download URL from Firebase Storage path
const getStorageUrl = async (path: string, type: ImageType = 'other'): Promise<string | null> => {
  if (!path) {
    console.warn('No path provided to getStorageUrl');
    return null;
  }

  const { storage } = getFirebaseServices();
  if (!storage) {
    console.error('Firebase Storage not initialized');
    return null;
  }

  try {
    // Remove any leading/trailing slashes and spaces
    let storagePath = path.replace(/^\/+|\/+$/g, '').trim();
    
    // If the path already includes the full path or is a gs:// URL, extract the relevant part
    if (storagePath.includes('gs://')) {
      // Extract just the path part after the bucket name if it's a gs:// URL
      const pathParts = storagePath.split('/');
      const bucketIndex = pathParts.findIndex(part => part.endsWith('.app'));
      if (bucketIndex >= 0) {
        storagePath = pathParts.slice(bucketIndex + 1).join('/');
      }
    } else {
      // Handle different image types and their storage paths
      switch (type) {
        case 'gallery':
          // For gallery images, they should be in the Website Images folder
          if (!storagePath.startsWith('Website%20Images/') && !storagePath.startsWith('Website Images/')) {
            storagePath = `Website Images/${storagePath}`;
          }
          break;
          
        case 'partner':
          // For partner logos, they should be in the Partner Logos folder
          if (!storagePath.startsWith('Partner%20Logos/') && !storagePath.startsWith('Partner Logos/')) {
            storagePath = `Partner Logos/${storagePath}`;
          }
          break;
          
        case 'news':
          // For news images, if path contains a folder, use as-is; otherwise prepend 'news/'
          if (!storagePath.includes('/')) {
            storagePath = `news/${storagePath}`;
          }
          // else, use storagePath as-is
          break;
          
        default:
          // For any other type, use the path as is
          console.log(`Using path as is for type '${type}':`, storagePath);
          break;
      }
    }
    
    // Decode URI components in the path (handles %20 -> space, etc.)
    const decodedPath = decodeURIComponent(storagePath);
    
    console.log(`Getting URL for type '${type}':`, { 
      originalPath: path,
      storagePath: decodedPath,
      type
    });
    
    const storageRef = ref(storage, decodedPath);
    console.log('Storage reference created:', storageRef.fullPath);
    
    const url = await getDownloadURL(storageRef);
    console.log(`Successfully got URL for ${decodedPath}`, url);
    return url;
    
  } catch (error) {
    console.error('Error getting download URL:', { 
      path, 
      type, 
      error: error instanceof Error ? error.message : String(error),
      errorDetails: error
    });
    return null;
  }
};

// Function to fetch gallery images from Firebase Storage
const getGalleryImages = async (): Promise<GalleryImage[]> => {
  const { storage } = getFirebaseServices();
  if (!storage) {
    console.error('Firebase Storage not initialized');
    return [];
  }

  try {
    console.log('Fetching gallery images from Firebase Storage...');
    
    // Use the direct storage images function to get images from the website images folder
    const images = await getDirectStorageImages();
    
    console.log(`Successfully loaded ${images.length} gallery images`);
    
    // If we have images, return them directly (they already have proper metadata)
    if (images.length > 0) {
      return images;
    }
    
    // Fallback to placeholder if no images found
    console.log('No images found in storage, falling back to placeholder');
    return [];
    
  } catch (error) {
    console.error('Error in getGalleryImages:', error);
    
    // Fallback to empty array on error
    return [];
  }
};

const getPartners = async (): Promise<Partner[]> => {
  const { db } = getFirebaseServices();
  if (!db) {
    console.error('Firestore not initialized');
    return [];
  }
  try {
    console.log('Fetching partners from Firestore...');
    const partnersRef = collection(db, 'partners');
    const snapshot = await getDocs(partnersRef);
    
    console.log(`Found ${snapshot.size} partner documents`);
    
    const partnerPromises = snapshot.docs.map(async (doc) => {
      const data = doc.data();
      
      try {
        // Log the raw logo path for debugging
        console.log(`Processing partner ${doc.id}:`, { 
          name: data.name,
          rawLogoPath: data.logo,
          hasLogo: !!data.logo 
        });
        
        // Get the logo URL using the 'partner' type
        let logoUrl: string | undefined;
        if (data.logo) {
          try {
            const url = await getStorageUrl(data.logo, 'partner');
            if (url) {
              logoUrl = url;
              console.log(`Successfully got logo URL for ${data.name}:`, logoUrl);
            }
          } catch (logoError) {
            console.error(`Error getting logo URL for ${data.name}:`, logoError);
          }
        }
        
        const partner: Partner = {
          id: doc.id,
          name: data.name || 'Partner',
          description: data.description || '',
          logo: logoUrl,
          website: data.website || '#',
          category: data.category || 'Other',
          ...data // Include any additional fields
        };
        
        return partner;
      } catch (error) {
        console.error(`Error processing partner ${doc.id}:`, error);
        return null;
      }
    });
    
    // Wait for all partner processing to complete and filter out any null values
    const results = await Promise.all(partnerPromises);
    const validPartners = results.filter((partner): partner is Partner => partner !== null);
    
    console.log(`Successfully processed ${validPartners.length} partners`);
    return validPartners;
  } catch (error) {
    console.error('Error fetching partners:', error);
    return [];
  }
};

const getNewsArticles = async (): Promise<{
  featured: NewsArticle | null;
  articles: NewsArticle[];
  categories: string[];
}> => {
  const { db } = getFirebaseServices();
  if (!db) {
    console.error('Firestore not initialized');
    return {
      featured: null,
      articles: [],
      categories: ['all', 'general']
    };
  }

  try {
    const newsCollection = collection(db, 'news');
    const newsSnapshot = await getDocs(newsCollection);

    const articles: NewsArticle[] = [];
    let featured: NewsArticle | null = null;
    const categorySet = new Set<string>(['all']);

    // Process each news article
    for (const doc of newsSnapshot.docs) {
      const data = doc.data();

      // Only show published articles
      if (data.status !== 'published') continue;
      if (!data.title || !data.publishedAt) continue;

      try {
        // Use publishedAt as the article date
        let articleDate: Date;
        if (typeof data.publishedAt === 'string' || data.publishedAt instanceof Date) {
          articleDate = new Date(data.publishedAt);
        } else if (typeof data.publishedAt.toDate === 'function') {
          articleDate = data.publishedAt.toDate();
        } else {
          articleDate = new Date();
        }

        // Use featuredImagePath (preferred), featuredImage, or fallback to images[0]
        let imageUrl: string | undefined = undefined;
        if (data.featuredImagePath) {
          imageUrl = data.featuredImagePath;
        } else if (data.featuredImage) {
          imageUrl = data.featuredImage;
        } else if (Array.isArray(data.imagesPaths) && data.imagesPaths.length > 0) {
          imageUrl = data.imagesPaths[0];
        } else if (Array.isArray(data.images) && data.images.length > 0) {
          imageUrl = data.images[0];
        }
        // Note: Image path processing is handled by getStorageUrl function

        // Normalize categories (array of strings)
        let categories: string[] = [];
        if (Array.isArray(data.categories)) {
          categories = data.categories.filter((c: any) => typeof c === 'string' && c.trim()).map((c: string) => c.toLowerCase().trim());
        }
        categories.forEach((cat) => {
          if (cat && cat !== 'general') categorySet.add(cat);
        });
        // Use first category as main category for compatibility
        const mainCategory = categories[0] || 'general';

        // Compose NewsArticle object
        const article: NewsArticle = {
          id: doc.id,
          title: data.title,
          excerpt: data.excerpt || '',
          summary: data.excerpt || '',
          content: data.content || '',
          imageUrl: imageUrl,
          category: mainCategory,
          date: articleDate,
          readTime: data.readTime || '2 min read',
          author: data.authorName || 'Admin',
          isFeatured: Boolean(data.featured) || false
        };

        // Set as featured if marked
        if (article.isFeatured && !featured) {
          featured = article;
        } else {
          articles.push(article);
        }
      } catch (error) {
        console.error(`Error processing news article ${doc.id}:`, error);
      }
    }

    // Sort articles by date (newest first)
    articles.sort((a, b) => (b.date as Date).getTime() - (a.date as Date).getTime());

    // If no featured article, use the most recent one
    if (!featured && articles.length > 0) {
      featured = articles.shift()!;
      featured.isFeatured = true;
    }

    // Convert Set to array for the return value
    const categories = Array.from(categorySet);

    return {
      featured,
      articles,
      categories
    };
  } catch (error) {
    console.error('Error fetching news articles:', error);
    return {
      featured: null,
      articles: [],
      categories: ['all', 'general']
    };
  }
};

// Define the Firestore image data type
interface FirestoreImageData {
  path?: string;
  fileName?: string;
  title?: string;
  description?: string;
  category?: string;
  uploadedAt?: Date | { toDate: () => Date };
  [key: string]: any;
}

// Helper function to convert Firestore timestamp to Date
const toDate = (value: any): Date => {
  if (!value) return new Date();
  if (value instanceof Date) return value;
  if (typeof value.toDate === 'function') return value.toDate();
  return new Date();
};

// Function to fetch gallery images from Firestore
const getGalleryImagesFromFirestore = async (): Promise<GalleryImage[]> => {
  const { db } = getFirebaseServices();
  if (!db) {
    console.error('Firestore not initialized');
    return [];
  }

  try {
    console.log('Fetching gallery images from Firestore...');
    const imagesRef = collection(db, 'galleryImages');
    const snapshot = await getDocs(imagesRef);
    
    const images: GalleryImage[] = [];
    
    for (const doc of snapshot.docs) {
      const data = doc.data() as FirestoreImageData;
      
      // Skip if no path is provided
      if (!data.path && !data.fileName) {
        console.warn(`Document ${doc.id} has no path or fileName`);
        continue;
      }
      
      try {
        // Use the path if available, otherwise use fileName
        const imagePath = data.path || data.fileName || '';
        console.log(`Processing image ${doc.id}:`, { imagePath });
        
        const imageUrl = await getStorageUrl(imagePath, 'gallery');
        
        if (imageUrl) {
          images.push({
            id: doc.id,
            url: imageUrl,
            title: data.title || 'Untitled',
            description: data.description || '',
            category: data.category || 'uncategorized',
            uploadedAt: data.uploadedAt ? toDate(data.uploadedAt) : new Date()
          });
        } else {
          console.warn(`Failed to get URL for image ${doc.id}: ${imagePath}`);
        }
      } catch (error) {
        console.error(`Error processing image ${doc.id}:`, error);
      }
    }
    
    console.log(`Fetched ${images.length} gallery images`);
    return images;
  } catch (error) {
    console.error('Error fetching gallery images:', error);
    throw error;
  }
};

// Function to get direct storage URLs for gallery images from the website images folder
const getDirectStorageImages = async (): Promise<GalleryImage[]> => {
  const { storage } = getFirebaseServices();
  if (!storage) {
    console.error('Firebase Storage not initialized');
    return [];
  }

  console.log('Initializing getDirectStorageImages...');
  console.log('Storage bucket:', storage.app.options.storageBucket);

  try {
    console.log('Fetching images directly from storage...');
    
    // Try both variations of the folder name
    const folderNames = ['website images', 'Website Images'];
    
    for (const folderName of folderNames) {
      try {
        console.log(`Attempting to access folder: "${folderName}"`);
        
        // Create a reference to the folder
        const folderRef = ref(storage, folderName);
        console.log(`Created reference to folder: ${folderRef.toString()}`);
        
        // List all items in the folder
        console.log('Listing items in folder...');
        const result = await listAll(folderRef);
        console.log(`Found ${result.items.length} items in folder "${folderName}"`);
        
        if (result.items.length === 0) {
          console.warn(`No items found in folder "${folderName}"`);
          continue; // Try next folder variation
        }
        
        // Log first few items for debugging
        console.log('Sample items in folder:');
        result.items.slice(0, Math.min(3, result.items.length)).forEach((item, i) => {
          console.log(`  ${i + 1}. ${item.name}`);
        });
        
        // Process each image file
        const images: GalleryImage[] = [];
        
        // Process items in batches to avoid overwhelming the client
        const batchSize = 5;
        for (let i = 0; i < result.items.length; i += batchSize) {
          const batch = result.items.slice(i, i + batchSize);
          console.log(`Processing batch ${i / batchSize + 1} of ${Math.ceil(result.items.length / batchSize)}`);
          
          const batchPromises = batch.map(async (item) => {
            try {
              console.log(`Getting download URL for: ${item.fullPath}`);
              const url = await getDownloadURL(item);
              console.log(`Successfully got URL for ${item.name}`);
              
              // Create a clean title from the filename
              const cleanName = item.name
                .replace(/[-_]/g, ' ') // Replace underscores and hyphens with spaces
                .replace(/\.[^/.]+$/, '') // Remove file extension
                .replace(/\b\w/g, (char) => char.toUpperCase()); // Capitalize first letter of each word
              
              return {
                id: item.name,
                url,
                title: cleanName,
                description: cleanName, // Removed 'Image:' prefix
                category: 'gallery',
                uploadedAt: new Date(),
                fullUrl: url,
                path: item.fullPath,
                isFeatured: true
              };
            } catch (error: any) {
              console.error(`Error processing ${item.name}:`, error?.message || 'Unknown error');
              return null;
            }
          });
          
          // Wait for all promises in the batch to complete
          const batchResults = await Promise.all(batchPromises);
          // Filter out any null results from failed operations
          const validImages = batchResults.filter((img): img is NonNullable<typeof img> => img !== null);
          images.push(...validImages);
          
          // Small delay between batches to avoid rate limiting
          if (i + batchSize < result.items.length) {
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }
        
        console.log(`Successfully processed ${images.length} images from folder "${folderName}"`);
        return images;
        
      } catch (error: any) {
        console.error(`Error accessing folder "${folderName}":`, error?.message || 'Unknown error');
        console.error('Error details:', {
          code: error?.code,
          message: error?.message,
          serverResponse: error?.serverResponse,
          name: error?.name
        });
        
        // Continue to next folder variation if this one fails
        continue;
      }
    }
    
    // If we got here, we failed to access all folder variations
    console.error('Failed to access all folder variations');
    return [];
    
  } catch (error) {
    console.error('Error in getDirectStorageImages:', error);
    // Don't throw here, let the caller handle the empty array case
    return [];
  }
};

// Function to set up real-time listener for gallery images
const onGalleryImagesUpdate = (
  onUpdate: (images: GalleryImage[]) => void,
  onError?: (error: Error) => void
): Unsubscribe => {
  const { db } = getFirebaseServices();
  if (!db) {
    console.error('Firestore not initialized');
    onError?.(new Error('Firestore not initialized'));
    return () => {}; // Return empty unsubscribe function
  }

  const imagesCollection = collection(db, 'images');
  
  return onSnapshot(
    imagesCollection, 
    (snapshot: QuerySnapshot<DocumentData>) => {
      const images: GalleryImage[] = [];
      snapshot.forEach((doc: DocumentSnapshot<DocumentData>) => {
        const data = doc.data();
        if (data) {
          images.push({
            id: doc.id,
            url: data.downloadURL || '',
            title: data.title || '',
            description: data.description || '',
            category: data.category || 'uncategorized',
            uploadedAt: data.uploadedAt?.toDate?.() || new Date(),
            ...data
          });
        }
      });
      onUpdate(images);
    },
    (error: Error) => {
      console.error('Error in gallery images listener:', error);
      onError?.(error);
    }
  );
};

// Get Firebase instances dynamically
const getFirebaseDb = () => getFirebaseServices().db;
const getFirebaseStorage = () => getFirebaseServices().storage;
const getFirebaseApp = () => getFirebaseServices().app;

// Export all Firebase services and utilities
export {
  // Firebase services
  getFirebaseDb as default,
  getFirebaseStorage as storage,
  getFirebaseApp as app,

  // Firebase utility functions
  getStorageUrl,
  getGalleryImages,
  getGalleryImagesFromFirestore,
  getDirectStorageImages,
  onGalleryImagesUpdate,
  getPartners,
  getNewsArticles,

  // Types
  type ImageType
};
