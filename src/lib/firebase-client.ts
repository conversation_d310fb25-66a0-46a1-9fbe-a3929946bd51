'use client';

// This file ensures Firebase is only initialized on the client side
import { initializeApp, getApps, getApp, FirebaseApp } from 'firebase/app';
import { getFirestore, Firestore } from 'firebase/firestore';
import { getStorage, FirebaseStorage } from 'firebase/storage';

// Firebase configuration with fallbacks
const getFirebaseConfig = () => {
  const config = {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  };

  // Log configuration status (without exposing sensitive data)
  if (typeof window !== 'undefined') {
    const configStatus = Object.entries(config).map(([key, value]) => ({
      [key]: value ? 'configured' : 'missing'
    }));
    console.log('Firebase config status:', configStatus);
  }

  return config;
};

let firebaseApp: FirebaseApp | null = null;
let db: Firestore | null = null;
let storage: FirebaseStorage | null = null;
let initializationAttempted = false;

// Initialize Firebase only on client side
export const initializeFirebaseClient = () => {
  // Always return null on server side
  if (typeof window === 'undefined') {
    return { app: null, db: null, storage: null };
  }

  // Return existing instances if already initialized
  if (firebaseApp && db && storage) {
    return { app: firebaseApp, db, storage };
  }

  // Prevent multiple initialization attempts
  if (initializationAttempted) {
    return { app: firebaseApp, db, storage };
  }

  initializationAttempted = true;

  try {
    const firebaseConfig = getFirebaseConfig();

    // Validate configuration - be more lenient in production
    const requiredFields = ['apiKey', 'authDomain', 'projectId', 'storageBucket'];
    const missingFields = requiredFields.filter(field => !firebaseConfig[field as keyof typeof firebaseConfig]);

    if (missingFields.length > 0) {
      console.error(`Missing Firebase configuration fields: ${missingFields.join(', ')}`);
      // In production, try to continue with partial config
      if (process.env.NODE_ENV === 'production') {
        console.warn('Continuing with partial Firebase configuration in production');
      } else {
        throw new Error(`Missing Firebase configuration fields: ${missingFields.join(', ')}`);
      }
    }

    // Initialize Firebase app
    if (!getApps().length) {
      firebaseApp = initializeApp(firebaseConfig);
      console.log('Firebase app initialized successfully');
    } else {
      firebaseApp = getApp();
      console.log('Using existing Firebase app');
    }

    // Initialize services
    if (firebaseApp) {
      try {
        db = getFirestore(firebaseApp);
        console.log('Firestore initialized successfully');
      } catch (error) {
        console.error('Firestore initialization failed:', error);
      }

      try {
        storage = getStorage(firebaseApp);
        console.log('Storage initialized successfully');
      } catch (error) {
        console.error('Storage initialization failed:', error);
      }
    }

    return { app: firebaseApp, db, storage };
  } catch (error) {
    console.error('Firebase initialization error:', error);
    // Return partial services if available
    return { app: firebaseApp, db, storage };
  }
};

// Get Firebase instances (client-side only)
export const getFirebaseInstances = () => {
  if (typeof window === 'undefined') {
    return { app: null, db: null, storage: null };
  }
  
  if (!firebaseApp || !db || !storage) {
    return initializeFirebaseClient();
  }
  
  return { app: firebaseApp, db, storage };
};
