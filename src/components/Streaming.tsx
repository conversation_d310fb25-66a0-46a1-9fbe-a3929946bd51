'use client';

import React, { useState } from 'react';

const Streaming = () => {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [pinCode, setPinCode] = useState(['', '', '', '', '', '']);

  const handlePinChange = (index: number, value: string) => {
    if (value.length <= 1 && /^[0-9]*$/.test(value)) {
      const newPin = [...pinCode];
      newPin[index] = value;
      setPinCode(newPin);

      // Auto-focus next input
      if (value && index < 5) {
        const nextInput = document.getElementById(`pin-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  const handlePinSubmit = () => {
    const fullPin = pinCode.join('');
    if (fullPin.length === 6) {
      alert('PIN verification will be available when the stream goes live.');
    } else {
      alert('Please enter a complete 6-digit PIN code.');
    }
  };

  const addToCalendar = (event: any, type: 'google' | 'outlook') => {
    const startDate = new Date('2025-06-25T10:00:00'); // Example date
    const endDate = new Date('2025-06-25T16:00:00');

    const formatDate = (date: Date) => {
      return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
    };

    if (type === 'google') {
      const googleUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(event.title)}&dates=${formatDate(startDate)}/${formatDate(endDate)}&details=${encodeURIComponent(event.description)}&location=${encodeURIComponent('Official Kingdom Website - Live Stream')}`;
      window.open(googleUrl, '_blank');
    } else if (type === 'outlook') {
      const outlookUrl = `https://outlook.live.com/calendar/0/deeplink/compose?subject=${encodeURIComponent(event.title)}&startdt=${formatDate(startDate)}&enddt=${formatDate(endDate)}&body=${encodeURIComponent(event.description + '\n\nWatch on: Official Kingdom Website')}&location=${encodeURIComponent('Official Kingdom Website - Live Stream')}`;
      window.open(outlookUrl, '_blank');
    }
  };
  
  const upcomingStreams = [
    {
      id: 1,
      title: 'Royal Coronation Ceremony',
      date: 'August 29, 2025',
      time: '7:30 AM - 9:00 PM GMT',
      location: 'Royal Palace Grounds, Adukrom',
      description: 'Join us for the grand coronation ceremony of His Royal Majesty. Experience the rich cultural heritage and traditional rites of passage.',
      category: 'Cultural'
    },
    {
      id: 2,
      title: 'Royal Gala Dinner',
      date: 'August 30, 2025',
      time: '9:30 AM - 10:00 PM GMT',
      location: 'Safari Valley Resort',
      description: 'An exclusive gala dinner with His Royal Majesty. An evening of fine dining, cultural performances, and royal entertainment.',
      category: 'Social'
    },
    {
      id: 3,
      title: 'Royal Economic Summit',
      date: 'August 31, 2025',
      time: '9:30 AM - 10:00 PM GMT',
      location: 'Safari Valley Resort',
      description: 'A forward-looking conference bringing together investors, entrepreneurs, and leaders to explore economic opportunities and partnerships with the Adukrom Kingdom.',
      category: 'Economic'
    }
  ];

  return (
    <section className="py-12 px-4 sm:px-6 lg:px-8 bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 text-white">
            Royal Event Streaming
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-gray-300 max-w-3xl mx-auto">
            Secure access to live royal events and ceremonies. Streams are available exclusively on the Official Kingdom Website during scheduled events.
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Main Video Player */}
          <div className={`${isChatOpen ? 'lg:w-2/3' : 'w-full'} bg-black rounded-xl overflow-hidden shadow-2xl`}>
            <div className="relative pt-[56.25%]">
              <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-gray-900 to-black">
                <div className="text-center p-8 max-w-md">
                  <div className="text-6xl mb-6">👑</div>
                  <h3 className="text-2xl font-bold mb-4 text-white">Royal Stream Access</h3>
                  <p className="text-gray-300 mb-6">Enter your 6-digit PIN code to access the live stream when available</p>

                  {/* PIN Code Input */}
                  <div className="flex justify-center gap-2 mb-6">
                    {pinCode.map((digit, index) => (
                      <input
                        key={index}
                        id={`pin-${index}`}
                        type="text"
                        value={digit}
                        onChange={(e) => handlePinChange(index, e.target.value)}
                        className="w-12 h-12 text-center text-xl font-bold bg-gray-800 border-2 border-gray-600 rounded-lg text-white focus:border-royalGold focus:outline-none"
                        maxLength={1}
                      />
                    ))}
                  </div>

                  <button
                    onClick={handlePinSubmit}
                    className="px-6 py-3 bg-royalGold text-royalBlue font-bold rounded-lg hover:bg-yellow-500 transition-colors mb-4"
                  >
                    Verify PIN
                  </button>

                  <div className="text-sm text-gray-400">
                    <p className="mb-2">🔒 Secure access only on the Official Kingdom Website</p>
                    <p>Stream will be available during scheduled events</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="p-4 bg-gray-800">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-xl font-bold text-white">
                    Royal Events Live Stream
                  </h3>
                  <p className="text-sm text-gray-300">
                    🔒 Available only on Official Kingdom Website • Next stream: June 25, 2025
                  </p>
                </div>
                <button
                  onClick={() => setIsChatOpen(!isChatOpen)}
                  className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center"
                >
                  <span className="mr-2">📅</span>
                  {isChatOpen ? 'Hide Schedule' : 'Show Schedule'}
                </button>
              </div>
            </div>
          </div>

          {/* Chat or Upcoming Streams */}
          <div className={`${isChatOpen ? 'lg:w-1/3' : 'hidden lg:block lg:w-1/3'} bg-gray-800 rounded-xl overflow-hidden shadow-xl`}>
            {isChatOpen ? (
              <div className="h-full flex flex-col">
                <div className="p-4 border-b border-gray-700">
                  <h3 className="font-bold">Live Chat</h3>
                </div>
                <div className="flex-1 p-4 overflow-y-auto">
                  <div className="space-y-4">
                    {chatMessages.map((message, index) => (
                      <div key={index} className="flex">
                        <div className="flex-shrink-0 h-8 w-8 rounded-full bg-gray-600 flex items-center justify-center text-sm font-bold mr-3">
                          {message.user.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <div className="font-bold text-sm">{message.user}</div>
                          <div className="text-sm">{message.text}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="p-4 border-t border-gray-700">
                  <div className="flex">
                    <input
                      type="text"
                      placeholder="Send a message..."
                      className="flex-1 bg-gray-700 text-white rounded-l-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-royalGold"
                    />
                    <button className="bg-royalGold text-white px-4 rounded-r-lg hover:bg-yellow-500">
                      Send
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="p-6">
                <h3 className="text-xl font-bold mb-4 text-white">Upcoming Streams</h3>
                <div className="space-y-4">
                  {upcomingStreams.map((stream) => (
                    <div key={stream.id} className="bg-gray-700/50 p-4 rounded-lg border border-gray-600">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-xs px-2 py-1 bg-royalGold/20 text-royalGold rounded-full">
                          {stream.category}
                        </span>
                      </div>
                      <div className="text-sm text-royalGold font-medium mb-1">
                        {stream.date} • {stream.time}
                      </div>
                      <h4 className="font-bold text-lg mb-1 text-white">{stream.title}</h4>
                      <p className="text-xs text-gray-400 mb-2">📍 {stream.location}</p>
                      <p className="text-sm text-gray-300 mb-3">{stream.description}</p>

                      <div className="flex gap-2">
                        <div className="relative group">
                          <button className="text-sm text-royalGold hover:underline font-medium">
                            📅 Set Reminder
                          </button>
                          <div className="absolute bottom-full left-0 mb-2 hidden group-hover:block bg-gray-800 rounded-lg p-2 shadow-xl border border-gray-600 z-10">
                            <button
                              onClick={() => addToCalendar(stream, 'google')}
                              className="block w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 rounded"
                            >
                              📧 Google Calendar
                            </button>
                            <button
                              onClick={() => addToCalendar(stream, 'outlook')}
                              className="block w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 rounded"
                            >
                              📅 Outlook Calendar
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-6 p-4 bg-gray-700/30 rounded-lg border border-gray-600">
                  <h4 className="font-bold mb-2 text-white">Get Notified</h4>
                  <p className="text-sm text-gray-300 mb-3">
                    Never miss a live stream. Contact us to get notified when we go live.
                  </p>
                  <a
                    href="/contact"
                    className="w-full bg-royalGold text-royalBlue px-4 py-2 text-sm rounded-lg hover:bg-yellow-500 transition-colors font-bold text-center block"
                  >
                    Contact Us for Notifications
                  </a>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

// Mock data for chat messages
const chatMessages = [
  { user: 'Kwame', text: 'This is amazing! The ceremony is beautiful.' },
  { user: 'Ama', text: 'The traditional dances are incredible!' },
  { user: 'Kofi', text: 'From which part of Ghana is this dance from?' },
  { user: 'Esi', text: 'The colors are so vibrant!' },
  { user: 'Yaw', text: 'Long live the King!' },
];

export default Streaming;
