'use client';

import React from 'react';

// Custom styles for scrollbar
const customScrollbarStyles = `
  .custom-scrollbar::-webkit-scrollbar {
    width: 4px;
  }
  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(6, 182, 212, 0.5);
    border-radius: 2px;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(6, 182, 212, 0.8);
  }
`;

const Tickets = () => {
  // Coronation NFT Collection
  const nftCollection = [
    {
      id: 1,
      name: 'Bronze NFT Package',
      subtitle: 'Royal Stream Pass',
      price: 50,
      image: '/nft/bronz-coronation-nft.png',
      features: [
        '🎫 Bronze NFT Ticket (livestream + collectible)',
        '📷 Downloadable Official Photo Pack',
        '📕 Digital Program Booklet (schedule & details)',
        '📧 Blockchain Certificate of Attendance',
        '🪙 Royal NFC Tag',
        '🌐 Access to Private Online Watch Party'
      ],
      addOns: [
        '📀 Commemorative Video Download: $20',
        '📦 Merch Bundle Upgrade: $40–$75',
        '📜 NFT Art Poster Print: $35'
      ],
      popular: false,
      tier: 'bronze',
      supply: 'Limited'
    },
    {
      id: 2,
      name: 'Silver NFT Package',
      subtitle: 'The Noble Watchers',
      price: 750,
      image: '/nft/silver-coronation-nft.png',
      features: [
        '🎫 Silver NFT Ticket (stream access + collectible)',
        '📩 Mailed Postcard (with photo & thank-you)',
        '📕 Digital Coronation Booklet (interactive PDF)',
        '🧢 Branded Cap or Pin (shipped)',
        '✍️ Silver Pen with Logo',
        '🪙 Royal NFC Tag',
        '📰 Online Q&A Session with Prince Allen',
        '🎥 Behind-the-Scenes Video Access',
        '📧 Digital Certificate of Attendance',
        '🏶️ Optional Add-On: Physical Commemorative Item ($25 extra)'
      ],
      addOns: [
        '📀 Commemorative Video Download: $20',
        '📦 Merch Bundle Upgrade: $40–$75',
        '📜 NFT Art Poster Print: $35'
      ],
      popular: true,
      tier: 'silver',
      supply: 'Limited'
    },
    {
      id: 3,
      name: 'Gold NFT Package',
      subtitle: 'The Royal Circle',
      price: 2000,
      image: '/nft/gold-coronation-nft.png',
      features: [
        '🎫 Gold NFT Ticket (collectible + access pass)',
        '📸 Framed Royal Portrait (numbered & signed)',
        '✍️ Engraved Gold Pen (coronation seal)',
        '📕 Commemorative Booklet (history & vision)',
        '🏃️ Premium Embroidered T-shirt',
        '🪙 Royal NFC Tag',
        '🧢 Limited-Edition Hat or Cap',
        '🎮 VIP Badge & Lanyard',
        '🍷 Access to Post-Coronation Reception (if applicable)',
        '📧 Blockchain Certificate of Attendance',
        '📦 Premium Royal Box (collectible packaging)'
      ],
      addOns: [
        '📀 Commemorative Video Download: $20',
        '📦 Merch Bundle Upgrade: $40–$75',
        '📜 NFT Art Poster Print: $35'
      ],
      popular: false,
      tier: 'gold',
      supply: 'Limited'
    }
  ];



  return (
    <>
      <style jsx>{customScrollbarStyles}</style>
      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-900 via-gray-800 to-black relative overflow-hidden">
        {/* Blockchain Grid Background */}
        <div className="absolute inset-0 opacity-10">
          <div className="grid grid-cols-12 gap-1 h-full">
            {Array.from({ length: 144 }).map((_, i) => (
              <div key={i} className="border border-cyan-400/20 animate-pulse" style={{ animationDelay: `${i * 0.1}s` }}></div>
            ))}
          </div>
        </div>

        {/* Floating Blockchain Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-20 left-10 w-2 h-2 bg-cyan-400 rounded-full animate-ping"></div>
          <div className="absolute top-40 right-20 w-1 h-1 bg-royalGold rounded-full animate-pulse"></div>
          <div className="absolute bottom-32 left-1/4 w-1.5 h-1.5 bg-blue-400 rounded-full animate-bounce"></div>
          <div className="absolute bottom-20 right-1/3 w-1 h-1 bg-green-400 rounded-full animate-ping"></div>
        </div>

        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-cyan-400/10 border border-cyan-400/30 rounded-full mb-6">
              <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
              <span className="text-cyan-400 text-sm font-medium">BLOCKCHAIN VERIFIED</span>
            </div>

            <h1 className="text-5xl md:text-7xl font-bold text-white mb-4">
              <span className="bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
                The Crown of Africa
              </span>
            </h1>
            <h2 className="text-2xl md:text-3xl font-semibold text-royalGold mb-6">
              Rise of a New Era
            </h2>
            <div className="w-32 h-1 bg-gradient-to-r from-cyan-400 to-royalGold mx-auto mb-8"></div>

            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed mb-8">
              Step into a story that spans centuries—a story of kings, queens, warriors, and wisdom.
              The Adukrom Kingdom NFT Collection serves as a bridge between the ancient and the modern,
              offering a once-in-a-lifetime opportunity to own a piece of royal history.
            </p>

            <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-400">
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                Ethereum Blockchain
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-blue-400 rounded-full mr-2"></span>
                Smart Contract Verified
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-purple-400 rounded-full mr-2"></span>
                Limited Edition
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* NFT Collection Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-black via-gray-900 to-gray-800 relative">
        {/* Subtle Grid Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="w-full h-full" style={{
            backgroundImage: 'linear-gradient(rgba(6, 182, 212, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(6, 182, 212, 0.1) 1px, transparent 1px)',
            backgroundSize: '50px 50px'
          }}></div>
        </div>

        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              NFT Tickets
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-cyan-400 to-royalGold mx-auto mb-6"></div>
            <p className="text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Choose your tier and become part of the royal legacy. Each NFT includes exclusive access and commemorative items.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
            {nftCollection.map((nft) => (
              <div
                key={nft.id}
                className={`relative rounded-3xl overflow-hidden backdrop-blur-xl border transition-all duration-500 hover:-translate-y-3 group flex flex-col h-full ${
                  nft.tier === 'bronze' ? 'bg-gradient-to-br from-orange-900/20 via-orange-800/10 to-orange-700/20 border-orange-400/30 hover:border-orange-400/60' :
                  nft.tier === 'silver' ? 'bg-gradient-to-br from-gray-700/20 via-gray-600/10 to-gray-500/20 border-gray-400/30 hover:border-gray-300/60' :
                  'bg-gradient-to-br from-yellow-600/20 via-yellow-500/10 to-yellow-400/20 border-yellow-400/30 hover:border-yellow-400/60'
                } ${nft.popular ? 'ring-2 ring-cyan-400/50 scale-105' : ''}`}
              >
                {nft.popular && (
                  <div className="absolute top-0 right-0 bg-gradient-to-r from-cyan-400 to-blue-500 text-white text-xs font-bold px-4 py-2 rounded-bl-2xl z-10">
                    <span className="flex items-center">
                      <span className="w-2 h-2 bg-white rounded-full mr-1 animate-pulse"></span>
                      MOST POPULAR
                    </span>
                  </div>
                )}

                {/* Blockchain Badge */}
                <div className="absolute top-4 left-4 z-10">
                  <div className="flex items-center px-3 py-1 bg-black/50 backdrop-blur-sm border border-cyan-400/30 rounded-full">
                    <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
                    <span className="text-cyan-400 text-xs font-medium">VERIFIED</span>
                  </div>
                </div>

                {/* NFT Image */}
                <div className="relative h-80 overflow-hidden">
                  <img
                    src={nft.image}
                    alt={nft.name}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent"></div>

                  {/* Tier Badge */}
                  <div className="absolute bottom-4 left-4">
                    <div className={`px-4 py-2 rounded-full font-bold text-sm backdrop-blur-sm ${
                      nft.tier === 'bronze' ? 'bg-orange-500/80 text-white border border-orange-400/50' :
                      nft.tier === 'silver' ? 'bg-gray-400/80 text-gray-900 border border-gray-300/50' :
                      'bg-yellow-500/80 text-yellow-900 border border-yellow-400/50'
                    }`}>
                      {nft.tier.toUpperCase()} TIER
                    </div>
                  </div>

                  {/* Supply Badge */}
                  <div className="absolute bottom-4 right-4">
                    <div className="px-3 py-1 bg-black/60 backdrop-blur-sm border border-white/20 rounded-full">
                      <span className="text-white text-xs font-medium">{nft.supply}</span>
                    </div>
                  </div>
                </div>

                <div className="p-8 flex flex-col h-full">
                  <div className="text-center mb-6">
                    <h3 className="text-2xl font-bold text-white mb-1">{nft.name}</h3>
                    <p className="text-gray-400 text-sm">{nft.subtitle}</p>
                  </div>

                  <div className="text-center mb-8">
                    <div className="flex items-center justify-center mb-2">
                      <span className="text-5xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">
                        ${nft.price}
                      </span>
                    </div>
                    <p className="text-gray-400 text-sm">Blockchain Verified</p>
                  </div>

                  {/* Features - Flexible content area */}
                  <div className="mb-8 flex-grow">
                    <h4 className="text-white font-semibold mb-4 text-center">Includes:</h4>
                    <div className="space-y-3 max-h-64 overflow-y-auto custom-scrollbar">
                      {nft.features.map((feature, index) => (
                        <div key={index} className="flex items-start text-gray-300 text-sm">
                          <span className="text-base mr-2 flex-shrink-0">{feature.split(' ')[0]}</span>
                          <span className="leading-relaxed">{feature.substring(feature.indexOf(' ') + 1)}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Add-ons */}
                  {nft.addOns && (
                    <div className="mb-8">
                      <h4 className="text-white font-semibold mb-3 text-center">Optional Add-Ons:</h4>
                      <div className="space-y-2">
                        {nft.addOns.map((addon, index) => (
                          <div key={index} className="text-gray-400 text-xs text-center">
                            {addon}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Button pushed to bottom */}
                  <div className="mt-auto">
                    <a
                      href={
                        nft.tier === 'bronze' ? 'https://tiike.com/event/109RCB' :
                        nft.tier === 'silver' ? 'https://tiike.com/event/108RCS' :
                        'https://tiike.com/event/107RCG'
                      }
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`w-full font-bold py-4 px-6 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-2xl transform hover:scale-105 inline-flex items-center justify-center ${
                        nft.tier === 'bronze' ? 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white' :
                        nft.tier === 'silver' ? 'bg-gradient-to-r from-gray-400 to-gray-500 hover:from-gray-500 hover:to-gray-600 text-gray-900' :
                        'bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-yellow-900'
                      }`}
                    >
                      <span className="flex items-center justify-center">
                        <span className="mr-2">🔗</span>
                        Buy {nft.tier.charAt(0).toUpperCase() + nft.tier.slice(1)} NFT
                      </span>
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Important Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
            {/* Shipping Information */}
            <div className="bg-gradient-to-br from-blue-900/20 via-blue-800/10 to-blue-700/20 backdrop-blur-xl border border-blue-400/30 rounded-3xl p-8">
              <div className="flex items-center mb-4">
                <span className="text-2xl mr-3">📦</span>
                <h3 className="text-xl font-bold text-white">Shipping & Delivery</h3>
              </div>
              <div className="space-y-3 text-gray-300">
                <p className="flex items-center">
                  <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                  Physical items take <strong className="text-white">4-6 weeks</strong> to arrive
                </p>
                <p className="flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                  Digital items delivered instantly
                </p>
                <p className="flex items-center">
                  <span className="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                  Worldwide shipping included
                </p>
                <p className="flex items-center">
                  <span className="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                  Tracking provided for all shipments
                </p>
              </div>
            </div>

            {/* Blockchain Information */}
            <div className="bg-gradient-to-br from-cyan-900/20 via-cyan-800/10 to-cyan-700/20 backdrop-blur-xl border border-cyan-400/30 rounded-3xl p-8">
              <div className="flex items-center mb-4">
                <span className="text-2xl mr-3">🔗</span>
                <h3 className="text-xl font-bold text-white">Blockchain Technology</h3>
              </div>
              <div className="space-y-3 text-gray-300">
                <p className="flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-3 animate-pulse"></span>
                  Ethereum blockchain secured
                </p>
                <p className="flex items-center">
                  <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                  Smart contract verified
                </p>
                <p className="flex items-center">
                  <span className="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                  Permanent ownership proof
                </p>
                <p className="flex items-center">
                  <span className="w-2 h-2 bg-cyan-400 rounded-full mr-3"></span>
                  Transferable & tradeable
                </p>
              </div>
            </div>
          </div>

          {/* Optional Add-Ons Section */}
          <div className="bg-gradient-to-br from-gray-900/40 via-gray-800/20 to-gray-700/40 backdrop-blur-xl border border-gray-400/20 rounded-3xl p-8 mb-16">
            <h3 className="text-2xl font-bold text-white mb-6 text-center">Optional Add-Ons (Available for All Packages)</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-6 bg-black/20 rounded-2xl border border-gray-600/30">
                <span className="text-3xl mb-3 block">📀</span>
                <h4 className="text-white font-semibold mb-2">Commemorative Video Download</h4>
                <p className="text-cyan-400 text-xl font-bold">$20</p>
              </div>
              <div className="text-center p-6 bg-black/20 rounded-2xl border border-gray-600/30">
                <span className="text-3xl mb-3 block">📦</span>
                <h4 className="text-white font-semibold mb-2">Merch Bundle Upgrade</h4>
                <p className="text-cyan-400 text-xl font-bold">$40–$75</p>
              </div>
              <div className="text-center p-6 bg-black/20 rounded-2xl border border-gray-600/30">
                <span className="text-3xl mb-3 block">📜</span>
                <h4 className="text-white font-semibold mb-2">NFT Art Poster Print</h4>
                <p className="text-cyan-400 text-xl font-bold">$35</p>
              </div>
            </div>
          </div>

          {/* Access Information */}
          <div className="bg-gradient-to-br from-purple-900/20 via-purple-800/10 to-purple-700/20 backdrop-blur-xl border border-purple-400/30 rounded-3xl p-8 text-center">
            <h3 className="text-2xl font-bold text-white mb-4">Secure Access</h3>
            <p className="text-gray-300 leading-relaxed">
              On the day of the stream, receive a unique pin code for secure access (one-time use).
              Your NFT serves as your permanent ticket and collectible proof of participation.
            </p>
          </div>
        </div>
      </section>


    </>
  );
};

export default Tickets;
