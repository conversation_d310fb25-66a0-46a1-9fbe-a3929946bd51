'use client';

import React from 'react';

const Tickets = () => {
  // Coronation NFT Collection
  const nftCollection = [
    {
      id: 1,
      name: 'Bronze Coronation NFT',
      price: 50,
      image: '/nft/bronz-coronation-nft.png',
      features: [
        'Digital collectible artwork',
        'Commemorative coronation design',
        'Blockchain certificate of authenticity',
        'Limited edition bronze tier',
        'Exclusive community access'
      ],
      popular: false,
      tier: 'bronze',
      supply: '1000 NFTs'
    },
    {
      id: 2,
      name: 'Silver Coronation NFT',
      price: 150,
      image: '/nft/silver-coronation-nft.png',
      features: [
        'Premium digital artwork',
        'Enhanced coronation design',
        'Blockchain certificate of authenticity',
        'Limited edition silver tier',
        'VIP community access',
        'Special utility benefits'
      ],
      popular: true,
      tier: 'silver',
      supply: '500 NFTs'
    },
    {
      id: 3,
      name: 'Gold Coronation NFT',
      price: 300,
      image: '/nft/gold-coronation-nft.png',
      features: [
        'Ultra-premium digital artwork',
        'Exclusive royal coronation design',
        'Blockchain certificate of authenticity',
        'Ultra-limited edition gold tier',
        'Royal community access',
        'Maximum utility benefits',
        'Future airdrop eligibility'
      ],
      popular: false,
      tier: 'gold',
      supply: '100 NFTs'
    }
  ];

  // Event Tickets
  const ticketOptions = [
    {
      id: 1,
      name: 'Standard Admission',
      price: 50,
      features: [
        'General seating',
        'Event program',
        'Commemorative gift'
      ],
      popular: false
    },
    {
      id: 2,
      name: 'VIP Experience',
      price: 150,
      features: [
        'Premium seating',
        'Exclusive lounge access',
        'Meet & greet opportunity',
        'Commemorative gift',
        'VIP parking'
      ],
      popular: true
    },
    {
      id: 3,
      name: 'Royal Package',
      price: 300,
      features: [
        'Front row seating',
        'Exclusive backstage tour',
        'Photo with His Royal Majesty',
        'Gourmet dining experience',
        'VIP parking',
        'Limited edition souvenir'
      ],
      popular: false
    }
  ];

  return (
    <>
      {/* Coronation NFT Collection Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-4">
              Coronation NFT Collection
            </h2>
            <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
            <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
              Own a piece of history with our exclusive Coronation NFT Collection. Each NFT commemorates
              the historic coronation of His Royal Majesty King Allen Ellison.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {nftCollection.map((nft) => (
              <div
                key={nft.id}
                className={`relative rounded-2xl overflow-hidden shadow-2xl bg-white/10 backdrop-blur-lg border border-white/20 hover:shadow-3xl transition-all duration-300 hover:-translate-y-2 ${
                  nft.popular ? 'ring-2 ring-royalGold transform scale-105' : ''
                }`}
              >
                {nft.popular && (
                  <div className="absolute top-0 right-0 bg-royalGold text-royalBlue text-xs font-bold px-4 py-2 rounded-bl-lg z-10">
                    MOST POPULAR
                  </div>
                )}

                {/* NFT Image */}
                <div className="relative h-64 overflow-hidden">
                  <img
                    src={nft.image}
                    alt={nft.name}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <div className="absolute bottom-4 left-4">
                    <span className={`px-3 py-1 rounded-full text-xs font-bold ${
                      nft.tier === 'bronze' ? 'bg-bronze text-white' :
                      nft.tier === 'silver' ? 'bg-gray-300 text-gray-800' :
                      'bg-royalGold text-royalBlue'
                    }`}>
                      {nft.tier.toUpperCase()} TIER
                    </span>
                  </div>
                </div>

                <div className="p-6">
                  <h3 className="text-2xl font-bold text-white mb-2">{nft.name}</h3>
                  <p className="text-white/70 text-sm mb-4">Supply: {nft.supply}</p>

                  <div className="mb-6">
                    <span className="text-4xl font-bold text-royalGold">${nft.price}</span>
                    <span className="text-white/70 ml-2">USD</span>
                  </div>

                  <ul className="space-y-2 mb-8">
                    {nft.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-white/80 text-sm">
                        <svg className="h-4 w-4 text-royalGold mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <button className="w-full bg-gradient-to-r from-royalGold to-yellow-500 text-royalBlue font-bold py-3 px-6 rounded-lg hover:from-yellow-500 hover:to-royalGold transition-all duration-300 shadow-lg hover:shadow-xl">
                    Mint NFT
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* NFT Information */}
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20">
            <h3 className="text-2xl font-bold text-white mb-4 text-center">About the Collection</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h4 className="text-lg font-bold text-royalGold mb-3">Blockchain Technology</h4>
                <p className="text-white/80 text-sm leading-relaxed">
                  Each NFT is minted on the Ethereum blockchain, ensuring permanent ownership and authenticity.
                  Your digital collectible is secured by the most trusted blockchain network.
                </p>
              </div>
              <div>
                <h4 className="text-lg font-bold text-royalGold mb-3">Utility & Benefits</h4>
                <p className="text-white/80 text-sm leading-relaxed">
                  NFT holders gain access to exclusive community events, future airdrops, and special privileges
                  within the Adukrom Kingdom ecosystem.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Event Tickets Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-royalBlue mb-4">Event Tickets</h2>
            <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Secure your spot at the most prestigious royal events of the year.
            </p>
          </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {ticketOptions.map((ticket) => (
            <div 
              key={ticket.id}
              className={`relative rounded-xl overflow-hidden shadow-lg ${
                ticket.popular ? 'ring-2 ring-royalGold transform -translate-y-2' : 'bg-white'
              }`}
            >
              {ticket.popular && (
                <div className="absolute top-0 right-0 bg-royalGold text-white text-xs font-bold px-4 py-1 rounded-bl-lg">
                  MOST POPULAR
                </div>
              )}
              <div className="p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{ticket.name}</h3>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-royalBlue">${ticket.price}</span>
                  <span className="text-gray-500">/person</span>
                </div>
                <ul className="space-y-3 mb-8">
                  {ticket.features.map((feature, index) => (
                    <li key={index} className="flex items-center">
                      <svg className="h-5 w-5 text-royalGold mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>
                <button className="w-full bg-royalBlue text-white font-medium py-3 px-6 rounded-lg hover:bg-royalBlue/90 transition-colors duration-300">
                  Purchase Now
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-16 bg-white rounded-xl shadow-lg p-8 max-w-4xl mx-auto">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">Group Bookings</h3>
          <p className="text-gray-600 mb-6">
            Planning to attend with a group? We offer special rates for groups of 10 or more. 
            Contact our events team for more information and to make arrangements.
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <input
              type="email"
              placeholder="Your email address"
              className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-royalGold focus:border-transparent"
            />
            <button className="bg-royalGold text-white font-medium py-3 px-6 rounded-lg hover:bg-royalGold/90 transition-colors duration-300">
              Contact Us
            </button>
          </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Tickets;
