'use client'

import Streaming from '../Streaming'

const StreamingPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-cream">
      {/* Hero Section - Connected to header */}
      <section className="relative pt-20 pb-20 overflow-hidden">
        {/* Background with gradients */}
        <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
        <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/20 via-royalGold/5 to-royalGold/15"></div>
        
        {/* Animated background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="royal-pattern w-full h-full"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center pt-12">
            <h1 className="text-5xl md:text-7xl font-serif font-bold text-white mb-6">
              Live Streaming
            </h1>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-royalGold mb-8">
              Royal Events & Ceremonies
            </h2>
            <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-8"></div>
            <p className="text-xl text-white/90 max-w-4xl mx-auto leading-relaxed">
              Experience the grandeur of Adukrom Kingdom's royal events from anywhere in the world. 
              Join us live for ceremonies, cultural celebrations, and important kingdom announcements.
            </p>
          </div>
        </div>
      </section>

      {/* Main Streaming Section */}
      <Streaming />

      {/* Additional Information Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl font-serif font-bold text-royalBlue mb-8 text-center">
              Streaming Information
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-16">
              <div>
                <h3 className="text-2xl font-bold text-royalBlue mb-4">How to Watch</h3>
                <p className="text-gray-700 leading-relaxed mb-4">
                  Our live streams are available on multiple platforms to ensure you never miss 
                  an important royal event. All streams are free and accessible worldwide.
                </p>
                <ul className="text-gray-700 space-y-2">
                  <li>• Official Kingdom Website</li>
                  <li>• YouTube Live Channel</li>
                  <li>• Facebook Live</li>
                  <li>• Mobile App (Coming Soon)</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-2xl font-bold text-royalBlue mb-4">Upcoming Events</h3>
                <p className="text-gray-700 leading-relaxed mb-4">
                  Stay updated with our event calendar to know when the next live stream 
                  will be available. We stream major ceremonies, cultural festivals, 
                  and special announcements.
                </p>
                <ul className="text-gray-700 space-y-2">
                  <li>• Royal Coronation Anniversary</li>
                  <li>• Cultural Festivals</li>
                  <li>• State Addresses</li>
                  <li>• Community Celebrations</li>
                </ul>
              </div>
            </div>

            {/* Technical Requirements */}
            <div className="bg-gradient-to-r from-royalBlue to-royalBlue/90 rounded-3xl p-8 text-white mb-12">
              <h3 className="text-2xl font-bold text-center mb-8">Technical Requirements</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="text-4xl font-bold text-royalGold mb-2">
                    <i className="fas fa-wifi"></i>
                  </div>
                  <h4 className="font-bold mb-2">Internet Connection</h4>
                  <p className="text-white/90 text-sm">Stable broadband connection recommended</p>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-royalGold mb-2">
                    <i className="fas fa-desktop"></i>
                  </div>
                  <h4 className="font-bold mb-2">Compatible Devices</h4>
                  <p className="text-white/90 text-sm">Desktop, mobile, tablet, smart TV</p>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-royalGold mb-2">
                    <i className="fas fa-play-circle"></i>
                  </div>
                  <h4 className="font-bold mb-2">Video Quality</h4>
                  <p className="text-white/90 text-sm">HD streaming with multiple quality options</p>
                </div>
              </div>
            </div>

            {/* Call to Action */}
            <div className="text-center">
              <h3 className="text-2xl font-bold text-royalBlue mb-4">Never Miss an Event</h3>
              <p className="text-gray-700 mb-6">
                Subscribe to our notifications to be alerted when we go live with royal events and ceremonies.
              </p>
              <a
                href="#contact"
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-royalGold to-yellow-500 text-royalBlue font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <i className="fas fa-bell mr-2"></i>
                Get Notifications
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default StreamingPage
