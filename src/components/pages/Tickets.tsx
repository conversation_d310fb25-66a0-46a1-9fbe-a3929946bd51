'use client'

import Tickets from '../Tickets'

const TicketsPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-cream">
      {/* Hero Section */}
      <section className="relative py-32 overflow-hidden">
        {/* Background with gradients */}
        <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
        <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/20 via-royalGold/5 to-royalGold/15"></div>
        
        {/* Animated background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="royal-pattern w-full h-full"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center">
            <h1 className="text-5xl md:text-7xl font-serif font-bold text-white mb-6">
              Event Tickets
            </h1>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-royalGold mb-8">
              Royal Events & Experiences
            </h2>
            <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-8"></div>
            <p className="text-xl text-white/90 max-w-4xl mx-auto leading-relaxed">
              Secure your place at exclusive royal events, cultural celebrations, and special ceremonies. 
              Experience the grandeur of Adukrom Kingdom's traditions and hospitality.
            </p>
          </div>
        </div>
      </section>

      {/* Main Tickets Section */}
      <Tickets />

      {/* Additional Information Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl font-serif font-bold text-royalBlue mb-8 text-center">
              Ticket Information
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-16">
              <div>
                <h3 className="text-2xl font-bold text-royalBlue mb-4">Ticket Types</h3>
                <p className="text-gray-700 leading-relaxed mb-4">
                  We offer various ticket categories to accommodate different preferences and budgets. 
                  Each ticket type provides unique access and experiences during royal events.
                </p>
                <ul className="text-gray-700 space-y-2">
                  <li>• VIP Royal Experience - Premium seating and exclusive access</li>
                  <li>• General Admission - Standard event access</li>
                  <li>• Cultural Package - Includes traditional meal and souvenirs</li>
                  <li>• Student/Senior Discounts - Special pricing available</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-2xl font-bold text-royalBlue mb-4">What's Included</h3>
                <p className="text-gray-700 leading-relaxed mb-4">
                  Your ticket includes access to the main event, cultural performances, 
                  traditional refreshments, and commemorative materials. VIP tickets 
                  include additional exclusive benefits.
                </p>
                <ul className="text-gray-700 space-y-2">
                  <li>• Event admission and reserved seating</li>
                  <li>• Traditional refreshments and meals</li>
                  <li>• Cultural performance access</li>
                  <li>• Commemorative program and materials</li>
                </ul>
              </div>
            </div>

            {/* Pricing Information */}
            <div className="bg-gradient-to-r from-royalBlue to-royalBlue/90 rounded-3xl p-8 text-white mb-12">
              <h3 className="text-2xl font-bold text-center mb-8">Pricing Guide</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="text-4xl font-bold text-royalGold mb-2">$50</div>
                  <h4 className="font-bold mb-2">General Admission</h4>
                  <p className="text-white/90 text-sm">Standard event access with basic amenities</p>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-royalGold mb-2">$100</div>
                  <h4 className="font-bold mb-2">Cultural Package</h4>
                  <p className="text-white/90 text-sm">Enhanced experience with traditional elements</p>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-royalGold mb-2">$200</div>
                  <h4 className="font-bold mb-2">VIP Royal Experience</h4>
                  <p className="text-white/90 text-sm">Premium access with exclusive benefits</p>
                </div>
              </div>
            </div>

            {/* Terms and Conditions */}
            <div className="bg-gray-50 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-royalBlue mb-4">Terms & Conditions</h3>
              <div className="text-gray-700 space-y-3">
                <p>• All ticket sales are final. No refunds except for event cancellation.</p>
                <p>• Tickets are non-transferable and must be presented with valid ID.</p>
                <p>• Event schedule and program subject to change without notice.</p>
                <p>• Photography and recording policies will be communicated at the event.</p>
                <p>• Dress code requirements apply for all royal events.</p>
                <p>• Children under 12 receive 50% discount with adult supervision.</p>
              </div>
            </div>

            {/* Call to Action */}
            <div className="text-center mt-12">
              <h3 className="text-2xl font-bold text-royalBlue mb-4">Ready to Join Us?</h3>
              <p className="text-gray-700 mb-6">
                Don't miss out on these exclusive royal experiences. Tickets are limited and sell out quickly.
              </p>
              <a
                href="#contact"
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-royalGold to-yellow-500 text-royalBlue font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <i className="fas fa-ticket-alt mr-2"></i>
                Purchase Tickets
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default TicketsPage