'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { FiSearch, FiArrowRight } from 'react-icons/fi';
import { FaHandshake, FaEnvelope, FaExternalLinkAlt, FaSpinner } from 'react-icons/fa';
import { Partner } from '@/types/partner';
import { getPartners } from '@/lib/firebase/firestore';
import { getStorage, ref, getDownloadURL } from 'firebase/storage';
import Image from 'next/image';
import Link from 'next/link';

interface PartnerWithLogoUrl extends Partner {
  logoUrl?: string;
}

const StrategicPartnersPage = () => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [partners, setPartners] = useState<PartnerWithLogoUrl[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeCategory, setActiveCategory] = useState<string>('all');

  // Filter partners based on search term and category
  const filteredPartners = useCallback((): PartnerWithLogoUrl[] => {
    const searchLower = searchTerm.toLowerCase();
    return partners.filter(partner => {
      const nameMatch = partner.name?.toLowerCase().includes(searchLower) ?? false;
      const descMatch = partner.description?.toLowerCase().includes(searchLower) ?? false;
      const matchesSearch = nameMatch || descMatch;
      const matchesCategory = activeCategory === 'all' || partner.category === activeCategory;
      return matchesSearch && matchesCategory;
    });
  }, [partners, searchTerm, activeCategory]);

  // Define partner links and descriptions mapping
  const partnerLinks: Record<string, { url: string; description: string }> = {
    'Remit Global Link': {
      url: 'https://remitglobalinc.com/',
      description: 'International money transfer and financial services'
    },
    'Royal Lion': {
      url: 'https://queenmotherlatonja.com/',
      description: 'Cultural preservation and community development'
    },
    'TEF The Ellison Foundation': {
      url: 'https://ellisonoutreachfoundation.com/',
      description: 'Educational and community outreach programs'
    },
    'LightAce Global': {
      url: 'https://lightaceglobal.wixsite.com/lightaceglobal',
      description: 'Global business solutions and consulting'
    },
    'Alpha Oriona': {
      url: 'https://www.alphaorionagroup.com/lander',
      description: 'Business development and strategic consulting'
    },
    'Infotegrity': {
      url: 'https://infotegriynetworkn.wixsite.com/infotegrity',
      description: 'Technology and information solutions'
    }
  };

  // Get filtered and processed partners list
  const getProcessedPartners = useCallback((): PartnerWithLogoUrl[] => {
    const filtered = filteredPartners();
    return filtered
      .filter(partner => partner.name && partner.name.toLowerCase() !== 'partner')
      .slice(0, 7);
  }, [filteredPartners]);

  // Get unique categories from partners
  const categories = ['all', ...Array.from(new Set(partners.map(partner => partner.category).filter(Boolean)))] as string[];

  useEffect(() => {
    const fetchPartners = async () => {
      // Only run on client side
      if (typeof window === 'undefined') return;

      try {
        setLoading(true);
        setError(null);

        // Fetch partners from Firestore
        const partnersData = await getPartners();

        // Get logo URLs for each partner
        const storage = getStorage();
        const partnersWithLogos = await Promise.all(
          partnersData.map(async (partner) => {
            if (partner.logo) {
              try {
                const logoRef = ref(storage, `partner/${partner.logo}`);
                const logoUrl = await getDownloadURL(logoRef);
                return { ...partner, logoUrl };
              } catch (err) {
                console.error(`Error loading logo for ${partner.name}:`, err);
                return partner; // Return partner without logo if there's an error
              }
            }
            return partner;
          })
        );

        setPartners(partnersWithLogos);
      } catch (err) {
        console.error('Error fetching partners:', err);
        setError('Failed to load partners. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchPartners();
  }, []);

  // Render loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="flex flex-col items-center">
          <FaSpinner className="animate-spin text-4xl text-blue-600 mb-4" />
          <p className="text-gray-600">Loading partners...</p>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8 bg-white rounded-lg shadow-md">
          <div className="text-red-500 text-5xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Error Loading Partners</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Get processed partners list
  const filteredPartnersList = getProcessedPartners();

  // If no partners found
  if (!filteredPartnersList || filteredPartnersList.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white flex items-center justify-center">
        <div className="text-center p-8 max-w-md mx-auto bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-100">
          <div>
            <div className="text-royalGold text-5xl mb-6">🤝</div>
            <h1 className="text-2xl font-bold text-royalBlue mb-3">
              No Partners Found
            </h1>
            <p className="text-gray-600 mb-6">
              We're currently working on bringing in new partners. Please check back soon!
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-2.5 bg-royalBlue text-white rounded-lg font-medium hover:bg-royalBlue/90 transition-all duration-300 shadow-md hover:shadow-lg hover:scale-105 active:scale-95"
            >
              Refresh
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden bg-gradient-to-r from-blue-600 to-blue-800">
        <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(to_bottom,transparent,black_20%,black_80%,transparent)]"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              Strategic Partners
            </h1>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              We collaborate with industry leaders to deliver exceptional value and innovation to our clients.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <button className="bg-white text-blue-700 px-6 py-3 rounded-lg font-medium flex items-center justify-center gap-2 hover:scale-105 active:scale-95 transition-transform">
                Become a Partner <FaHandshake className="inline" />
              </button>
              <button className="bg-blue-500 bg-opacity-20 text-white px-6 py-3 rounded-lg font-medium flex items-center justify-center gap-2 hover:scale-105 active:scale-95 transition-transform">
                Contact Us <FaEnvelope className="inline" />
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Partners Grid */}
      <section className="py-16 md:py-24 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-royalBlue mb-4">All of our partners</h2>
            <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
            <p className="text-gray-600 max-w-3xl mx-auto">
              We're proud to collaborate with these exceptional organizations that share our vision for progress and excellence.
            </p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {getProcessedPartners().map((partner: PartnerWithLogoUrl, index: number) => {
              const partnerInfo = partner.name ? partnerLinks[partner.name] : null;
              const hasLink = !!partnerInfo?.url;

              return (
                <div key={partner.id || index} className="h-full">
                  {hasLink ? (
                    <a
                      href={partnerInfo.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block h-full group hover:no-underline hover:scale-[1.02] active:scale-[0.98] transition-transform"
                    >
                      <div className="h-full bg-white/80 backdrop-blur-sm rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-royalGold/30 hover:-translate-y-1 flex flex-col">
                        <div className="p-6 flex-1 flex flex-col items-center justify-center">
                          {partner.logoUrl ? (
                            <div className="relative w-40 h-32 mb-6 flex items-center justify-center">
                              <img 
                                src={partner.logoUrl} 
                                alt={partner.name || 'Partner logo'} 
                                className="max-h-full max-w-full object-contain"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.onerror = null;
                                  target.src = '/images/placeholder-logo.png';
                                }}
                              />
                            </div>
                          ) : (
                            <div className="w-32 h-32 rounded-full bg-royalBlue/5 flex items-center justify-center mb-6">
                              <span className="text-4xl">🏢</span>
                            </div>
                          )}
                          
                          <h3 className="text-xl font-semibold text-gray-800 text-center mb-2">
                            {partner.name}
                          </h3>
                          
                          {partner.description && (
                            <p className="text-gray-600 text-center text-sm line-clamp-3">
                              {partner.description}
                            </p>
                          )}
                          
                          <div className="mt-4 text-royalBlue text-sm font-medium flex items-center justify-center gap-1">
                            Visit website
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                          </div>
                        </div>
                      </div>
                    </a>
                  ) : (
                    <div className="h-full bg-white/80 backdrop-blur-sm rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 p-6 flex flex-col items-center justify-center">
                      {partner.logoUrl ? (
                        <div className="relative w-40 h-32 mb-6 flex items-center justify-center">
                          <img 
                            src={partner.logoUrl} 
                            alt={partner.name || 'Partner logo'} 
                            className="max-h-full max-w-full object-contain"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.onerror = null;
                              target.src = '/images/placeholder-logo.png';
                            }}
                          />
                        </div>
                      ) : (
                        <div className="w-32 h-32 rounded-full bg-royalBlue/5 flex items-center justify-center mb-6">
                          <span className="text-4xl">🏢</span>
                        </div>
                      )}
                      
                      <h3 className="text-xl font-semibold text-gray-800 text-center mb-2">
                        {partner.name}
                      </h3>
                      
                      {partner.description && (
                        <p className="text-gray-600 text-center text-sm line-clamp-3">
                          {partner.description}
                        </p>
                      )}
                      {partnerInfo?.description && (
                        <p className="text-sm text-gray-500 text-center mt-2">
                          {partnerInfo.description}
                        </p>
                      )}
                      {partnerInfo?.url && (
                        <div className="mt-4">
                          <a 
                            href={partnerInfo.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-royalBlue hover:underline flex items-center justify-center gap-1"
                          >
                            {new URL(partnerInfo.url).hostname.replace('www.', '')}
                          </a>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-royalBlue/5 py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-6">Become a Strategic Partner</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
            Interested in collaborating with us? Let's explore how we can work together to create meaningful impact.
          </p>
          <Link 
            href="/contact"
            className="inline-block bg-royalBlue text-white px-8 py-3 rounded-lg font-medium hover:bg-royalBlue/90 transition-all duration-300 shadow-md hover:shadow-lg hover:scale-105 active:scale-95"
          >
            Get in Touch
          </Link>
        </div>
      </section>

      {/* Secondary CTA Section */}
      <section className="bg-gradient-to-r from-royalBlue to-royalBlue/90 py-16 md:py-24 relative overflow-hidden">
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0 bg-[url('/images/patterns/diamond-pattern.svg')] bg-repeat"></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <span className="text-royalGold font-semibold tracking-wider text-sm uppercase mb-4 inline-block">Join Our Network</span>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-white mb-6">Become a Strategic Partner</h2>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto mb-8 leading-relaxed">
              Partner with the Kingdom of Adukrom and contribute to shaping Africa's prosperous future through meaningful collaboration and shared vision.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link 
                href="/contact"
                className="bg-royalGold hover:bg-amber-500 text-white font-bold py-4 px-8 rounded-lg transition-all duration-300 inline-flex items-center justify-center hover:scale-105 active:scale-95 hover:shadow-lg hover:shadow-amber-500/30"
              >
                <FaHandshake className="mr-3" />
                Partner With Us
              </Link>
              <Link 
                href="/contact"
                className="border-2 border-white/30 hover:border-royalGold text-white font-bold py-4 px-8 rounded-lg transition-all duration-300 inline-flex items-center justify-center hover:scale-105 active:scale-95 hover:bg-white/10"
              >
                <FaEnvelope className="mr-3" />
                Contact Us
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

export default StrategicPartnersPage;