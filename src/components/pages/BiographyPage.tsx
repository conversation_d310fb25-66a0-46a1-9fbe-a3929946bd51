'use client'

import <PERSON><PERSON>llen from '../AboutAllen'

const BiographyPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-cream">
      {/* Hero Section */}
      <section className="relative py-32 overflow-hidden">
        {/* Background with gradients */}
        <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
        <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/20 via-royalGold/5 to-royalGold/15"></div>
        
        {/* Animated background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="royal-pattern w-full h-full"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center">
            <h1 className="text-5xl md:text-7xl font-serif font-bold text-white mb-6">
              His Royal Majesty
            </h1>
            <h2 className="text-4xl md:text-5xl font-serif font-bold text-royalGold mb-8">
              <PERSON>
            </h2>
            <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-8"></div>
            <p className="text-xl text-white/90 max-w-4xl mx-auto leading-relaxed">
              Mpuntuhene of Adukrom Kingdom - A visionary leader bridging tradition and innovation, 
              dedicated to empowering communities and fostering global partnerships.
            </p>
          </div>
        </div>
      </section>

      {/* Main Biography Section */}
      <AboutAllen />

      {/* Additional Biography Content */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl font-serif font-bold text-royalBlue mb-8 text-center">
              Leadership Philosophy
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-16">
              <div>
                <h3 className="text-2xl font-bold text-royalBlue mb-4">Vision for the Kingdom</h3>
                <p className="text-gray-700 leading-relaxed mb-4">
                  His Royal Majesty King Allen Ellison envisions Adukrom Kingdom as a model of sustainable 
                  development that honors traditional values while embracing modern opportunities. 
                  His leadership focuses on education, economic empowerment, and cultural preservation.
                </p>
                <p className="text-gray-700 leading-relaxed">
                  Through strategic partnerships and innovative initiatives, the King is positioning 
                  Adukrom as a beacon of progress in West Africa, demonstrating how traditional 
                  leadership can drive contemporary development.
                </p>
              </div>
              
              <div>
                <h3 className="text-2xl font-bold text-royalBlue mb-4">Global Impact</h3>
                <p className="text-gray-700 leading-relaxed mb-4">
                  King Allen Ellison's influence extends beyond the kingdom's borders through 
                  international diplomacy, cultural exchange programs, and strategic business partnerships. 
                  His work promotes African heritage on the global stage.
                </p>
                <p className="text-gray-700 leading-relaxed">
                  As a bridge between traditional African leadership and modern governance, 
                  His Majesty advocates for sustainable development, youth empowerment, 
                  and the preservation of cultural identity in an increasingly connected world.
                </p>
              </div>
            </div>

            {/* Achievements */}
            <div className="bg-gradient-to-r from-royalBlue to-royalBlue/90 rounded-3xl p-8 text-white">
              <h3 className="text-2xl font-bold text-center mb-8">Royal Achievements</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="text-4xl font-bold text-royalGold mb-2">
                    <i className="fas fa-crown"></i>
                  </div>
                  <h4 className="font-bold mb-2">Traditional Authority</h4>
                  <p className="text-white/90 text-sm">Mpuntuhene of the revered Nifahene Stool</p>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-royalGold mb-2">
                    <i className="fas fa-handshake"></i>
                  </div>
                  <h4 className="font-bold mb-2">Strategic Partnerships</h4>
                  <p className="text-white/90 text-sm">International collaborations for development</p>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-royalGold mb-2">
                    <i className="fas fa-seedling"></i>
                  </div>
                  <h4 className="font-bold mb-2">Sustainable Initiatives</h4>
                  <p className="text-white/90 text-sm">Leading eco-friendly development projects</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default BiographyPage
