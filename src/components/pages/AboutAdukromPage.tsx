'use client'

import AboutAdukrom from '../AboutAdukrom'

const AboutAdukromPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-cream">
      {/* Hero Section - Connected to header */}
      <section className="relative pt-20 pb-20 overflow-hidden">
        {/* Background with gradients */}
        <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
        <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/20 via-royalGold/5 to-royalGold/15"></div>
        
        {/* Animated background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="royal-pattern w-full h-full"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center pt-12">
            <h1 className="text-5xl md:text-7xl font-serif font-bold text-white mb-6">
              About Adukrom Kingdom
            </h1>
            <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-8"></div>
            <p className="text-xl text-white/90 max-w-4xl mx-auto leading-relaxed">
              Discover the rich heritage, strategic location, and visionary future of the Crown of Africa. 
              Nestled in Ghana's Eastern Region, Adukrom Kingdom stands as a beacon of tradition and progress.
            </p>
          </div>
        </div>
      </section>

      {/* Main About Section */}
      <AboutAdukrom showHeader={false} />

      {/* Additional Content Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl font-serif font-bold text-royalBlue mb-8 text-center">
              The Kingdom's Legacy
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-16">
              <div>
                <h3 className="text-2xl font-bold text-royalBlue mb-4">Historical Significance</h3>
                <p className="text-gray-700 leading-relaxed mb-4">
                  Adukrom has served as a vital center of governance and culture in the Akuapem region for generations. 
                  The Nifahene Stool represents one of the most respected traditional authorities in Ghana, 
                  with a legacy of wise leadership and community development.
                </p>
                <p className="text-gray-700 leading-relaxed">
                  Under the visionary leadership of His Royal Majesty King Allen Ellison, the kingdom continues 
                  to honor its traditions while embracing modern opportunities for growth and prosperity.
                </p>
              </div>
              
              <div>
                <h3 className="text-2xl font-bold text-royalBlue mb-4">Strategic Vision</h3>
                <p className="text-gray-700 leading-relaxed mb-4">
                  The kingdom's strategic location along major trade routes positions it as a gateway for 
                  economic development and international cooperation. Our vision encompasses sustainable 
                  development, cultural preservation, and global partnerships.
                </p>
                <p className="text-gray-700 leading-relaxed">
                  Through initiatives in education, agriculture, renewable energy, and tourism, 
                  Adukrom Kingdom is building a prosperous future while maintaining its cultural identity.
                </p>
              </div>
            </div>

            {/* Statistics */}
            <div className="bg-gradient-to-r from-royalBlue to-royalBlue/90 rounded-3xl p-8 text-white">
              <h3 className="text-2xl font-bold text-center mb-8">Kingdom at a Glance</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="text-4xl font-bold text-royalGold mb-2">500+</div>
                  <p className="text-white/90">Years of Heritage</p>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-royalGold mb-2">50K+</div>
                  <p className="text-white/90">Community Members</p>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-royalGold mb-2">15+</div>
                  <p className="text-white/90">Development Projects</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default AboutAdukromPage
