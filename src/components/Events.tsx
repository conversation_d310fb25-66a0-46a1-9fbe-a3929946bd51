'use client';

import React from 'react';

const Events = () => {
  const upcomingEvents = [
    {
      title: 'Royal Coronation Ceremony',
      description: 'Join us for the grand coronation ceremony of His Royal Majesty. Experience the rich cultural heritage and traditional rites of passage.',
      date: 'June 25, 2025',
      time: '10:00 AM - 4:00 PM',
      location: 'Royal Palace, Adukrom',
      category: 'Cultural',
      image: '/Website Images/Royal Coronation Ceremony.png',
      ticketsAvailable: true
    },
    {
      title: 'Royal Gala Dinner',
      description: 'An exclusive gala dinner with His Royal Majesty. An evening of fine dining, cultural performances, and royal entertainment.',
      date: 'June 26, 2025',
      time: '7:00 PM - 11:00 PM',
      location: 'Grand Ballroom, Adukrom',
      category: 'Social',
      image: '/Website Images/Royal Gala Dinner.png',
      ticketsAvailable: true
    },
    {
      title: 'Cultural Festival',
      description: 'Experience the vibrant culture of Adukrom through music, dance, and traditional performances from across the region.',
      date: 'June 27, 2025',
      time: '12:00 PM - 8:00 PM',
      location: 'Adukrom Town Square',
      category: 'Cultural',
      image: '/Website Images/ghana-osuodumgya-event-2023.png',
      ticketsAvailable: false
    },

  ];

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-royalBlue to-royalNavy">
      <div className="max-w-7xl mx-auto space-y-16">
        {/* Hero Section */}
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Upcoming <span className="text-royalGold">Events</span>
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-4"></div>
          <p className="text-white/80 max-w-2xl mx-auto">
            Mark your calendars for these extraordinary royal celebrations and cultural gatherings.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {upcomingEvents.map((event, index) => (
            <div
              key={index}
              className="group bg-white/10 backdrop-blur-lg rounded-3xl overflow-hidden border border-white/20 shadow-2xl hover:shadow-3xl hover:scale-105 hover:-translate-y-2 transition-all duration-300"
              style={{
                backgroundColor: 'rgba(30, 58, 138, 0.2)', // Royal blue with transparency
                backdropFilter: 'blur(16px)',
                borderColor: 'rgba(255, 255, 255, 0.3)'
              }}
            >
              {/* Event Header Image */}
              <div className="h-56 relative overflow-hidden">
                <img
                  src={event.image}
                  alt={event.title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                <div className="absolute top-4 left-4">
                  <span className="px-3 py-1 bg-royalGold/90 text-royalBlue text-xs font-bold rounded-full">
                    {event.category}
                  </span>
                </div>
                {event.ticketsAvailable && (
                  <div className="absolute top-4 right-4">
                    <span className="px-3 py-1 bg-green-500/90 text-white text-xs font-bold rounded-full">
                      Tickets Available
                    </span>
                  </div>
                )}
              </div>

              <div className="p-8">
                <h3 className="text-2xl font-bold text-white mb-3">{event.title}</h3>
                <p className="text-white/90 text-sm mb-6 leading-relaxed">{event.description}</p>

                <div className="space-y-3 mb-6">
                  <div className="flex items-center text-white/80">
                    <div className="w-8 h-8 bg-royalGold/20 rounded-lg flex items-center justify-center mr-3">
                      <i className="fas fa-calendar text-royalGold text-sm"></i>
                    </div>
                    <div>
                      <p className="text-sm font-semibold">{event.date}</p>
                      <p className="text-xs text-white/70">{event.time}</p>
                    </div>
                  </div>
                  <div className="flex items-center text-white/80">
                    <div className="w-8 h-8 bg-royalGold/20 rounded-lg flex items-center justify-center mr-3">
                      <i className="fas fa-map-marker-alt text-royalGold text-sm"></i>
                    </div>
                    <p className="text-sm">{event.location}</p>
                  </div>
                </div>

                <div className="flex gap-3">
                  <a
                    href="/rsvp"
                    className="flex-1 px-4 py-3 bg-white/25 border-2 border-white/60 text-white text-sm font-bold rounded-xl hover:bg-white/35 hover:border-white/80 transition-all duration-300 text-center shadow-lg hover:shadow-xl cursor-pointer"
                    style={{
                      backgroundColor: 'rgba(255, 255, 255, 0.2)',
                      borderColor: 'rgba(255, 255, 255, 0.5)',
                      textShadow: '0 1px 2px rgba(0, 0, 0, 0.4)',
                      zIndex: 10,
                      position: 'relative'
                    }}
                    onClick={() => {
                      console.log(`RSVP clicked for: ${event.title}`);
                    }}
                  >
                    RSVP
                  </a>
                  {event.ticketsAvailable && (
                    <a
                      href="/tickets"
                      className="px-6 py-3 relative overflow-hidden text-royalBlue font-bold text-sm rounded-xl shadow-2xl border-2 border-yellow-300 group"
                      style={{
                        background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
                        boxShadow: '0 8px 32px rgba(255, 215, 0, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.3), inset 0 -2px 4px rgba(0, 0, 0, 0.2)'
                      }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
                      <span className="relative z-10 font-extrabold text-shadow">Get Tickets</span>
                    </a>
                  )}
                </div>
              </div>

              {/* Hover effect overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-royalGold/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-3xl"></div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div
            className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl max-w-2xl mx-auto hover:scale-105 transition-all duration-500"
            style={{
              backgroundColor: 'rgba(30, 58, 138, 0.2)', // Royal blue with transparency
              backdropFilter: 'blur(16px)',
              borderColor: 'rgba(255, 255, 255, 0.3)'
            }}
          >
            <h3 className="text-2xl font-bold text-white mb-4">Stay Updated</h3>
            <p className="text-white/90 mb-6 leading-relaxed">
              Don't miss out on upcoming royal events and cultural celebrations. Subscribe to our newsletter for exclusive updates and early access to tickets.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/rsvp"
                className="inline-flex items-center px-8 py-4 relative overflow-hidden text-royalBlue font-bold rounded-xl shadow-2xl border-2 border-yellow-300 group hover:scale-110 hover:-translate-y-1 transition-all duration-300 cursor-pointer"
                style={{
                  background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
                  boxShadow: '0 8px 32px rgba(255, 215, 0, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.3), inset 0 -2px 4px rgba(0, 0, 0, 0.2)',
                  zIndex: 10,
                  position: 'relative'
                }}
                onClick={() => {
                  console.log('RSVP for Events button clicked');
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
                <span className="relative z-10 font-extrabold text-shadow flex items-center">
                  <i className="fas fa-calendar-check mr-2"></i>
                  RSVP for Events
                </span>
              </a>
              <a
                href="/contact"
                className="inline-flex items-center px-8 py-4 bg-white/20 backdrop-blur-md border border-white/30 text-white font-bold rounded-xl shadow-lg hover:shadow-xl hover:scale-110 hover:-translate-y-1 transition-all duration-300"
              >
                <i className="fas fa-envelope mr-2"></i>
                Contact Us
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Events;
