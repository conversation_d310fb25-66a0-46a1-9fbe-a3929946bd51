'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { getGalleryImages } from '@/lib/firebase';
import { GalleryImage } from '@/lib/firebase';

interface CarouselImage {
  url: string;
  title: string;
  description: string;
  category: string;
}
import { FaTimes, FaChevronLeft, FaChevronRight, FaImage } from 'react-icons/fa';

const RoyalGallery: React.FC<{ isHomepage?: boolean }> = ({ isHomepage = false }) => {
  const [images, setImages] = useState<GalleryImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [retryCount, setRetryCount] = useState(0);


  useEffect(() => {
    let isMounted = true;

    const fetchGalleryImages = async () => {
      // Only run on client side
      if (typeof window === 'undefined') return;

      try {
        if (isMounted) setLoading(true);
        const galleryImages = await getGalleryImages();

        if (isMounted) {
          setImages(galleryImages);
          setError(null);
        }
      } catch (err) {
        console.error('Error in fetchGalleryImages:', err);
        if (isMounted) {
          setError('Failed to load gallery images. ' + (err instanceof Error ? err.message : 'Please try again later.'));
        }
      } finally {
        if (isMounted) setLoading(false);
      }
    };

    fetchGalleryImages();
    
    return () => {
      isMounted = false;
    };
  }, [retryCount]);

  const openModal = (image: GalleryImage, index: number) => {
    setSelectedImage(image);
    setCurrentIndex(index);
    document.body.style.overflow = 'hidden';
  };

  const closeModal = () => {
    setSelectedImage(null);
    document.body.style.overflow = 'unset';
  };

  const navigate = (direction: 'prev' | 'next') => {
    if (!selectedImage || images.length <= 1) return;
    
    let newIndex;
    if (direction === 'next') {
      newIndex = (currentIndex + 1) % images.length;
    } else {
      newIndex = (currentIndex - 1 + images.length) % images.length;
    }
    
    setSelectedImage(images[newIndex]);
    setCurrentIndex(newIndex);
  };

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
  };

  // Partner logos with optimized image loading
  const partnerLogos = [
    { 
      id: 'remit-global', 
      logo: 'https://firebasestorage.googleapis.com/v0/b/your-project-id.appspot.com/o/partners%2Fremit-global.png?alt=media',
      name: 'Remit Global',
      width: 180,
      height: 80
    },
    { 
      id: 'royal-lion', 
      logo: 'https://firebasestorage.googleapis.com/v0/b/your-project-id.appspot.com/o/partners%2Froyal-lion.png?alt=media',
      name: 'Royal Lion',
      width: 180,
      height: 80
    },
    { 
      id: 'tef', 
      logo: 'https://firebasestorage.googleapis.com/v0/b/your-project-id.appspot.com/o/partners%2Ftef.png?alt=media',
      name: 'TEF',
      width: 180,
      height: 80
    },
    { 
      id: 'lightace', 
      logo: 'https://firebasestorage.googleapis.com/v0/b/your-project-id.appspot.com/o/partners%2Flightace.png?alt=media',
      name: 'LightAce Global',
      width: 180,
      height: 80
    },
    { 
      id: 'akuapem', 
      logo: 'https://firebasestorage.googleapis.com/v0/b/your-project-id.appspot.com/o/partners%2Fakuapem.png?alt=media',
      name: 'Akuapem Nifaman Council',
      width: 180,
      height: 80
    },
  ];

  // Carousel images with optimized loading and placeholders
  const carouselImages: CarouselImage[] = [
    {
      url: '/Website Images/gallery/ceremony.jpg',
      title: 'Royal Ceremony',
      description: 'Traditional royal ceremony showcasing our rich heritage',
      category: 'ceremony'
    },
    {
      url: '/Website Images/gallery/traditional-dance.jpg',
      title: 'Cultural Festival',
      description: 'Vibrant display of traditional dances and cultural heritage',
      category: 'culture'
    },
    {
      url: '/Website Images/gallery/community.jpg',
      title: 'Community Outreach',
      description: 'Royal family engaging with local community members',
      category: 'community'
    },
    {
      url: '/Website Images/gallery/palace.jpg',
      title: 'Royal Palace',
      description: 'The magnificent royal palace in all its glory',
      category: 'architecture'
    },
    {
      url: '/Website Images/gallery/coronation1.jpg',
      title: 'Coronation Ceremony',
      description: 'Historic coronation ceremony of His Royal Majesty',
      category: 'ceremony'
    },
    {
      url: '/Website Images/gallery/artifacts.jpg',
      title: 'Royal Artifacts',
      description: 'Sacred artifacts and treasures of the kingdom',
      category: 'artifacts'
    }
  ];
  

  
  // Function to handle opening modal with proper typing
  const handleOpenModal = (image: CarouselImage | GalleryImage, index: number) => {
    if ('id' in image) {
      // It's a GalleryImage
      openModal(image, index);
    } else {
      // It's a CarouselImage - create a compatible object
      const galleryImage: GalleryImage = {
        id: `carousel-${index}`,
        url: image.url,
        title: image.title,
        description: image.description,
        category: image.category,
        uploadedAt: new Date()
      };
      openModal(galleryImage, index);
    }
  };
  




  if (loading) {
    return (
      <div className="py-12">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-8">Royal Gallery</h2>
          <div className="flex justify-center">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex-shrink-0 w-full sm:w-1/2 md:w-1/3 px-2">
                <div className="bg-gray-200 rounded-lg animate-pulse aspect-square flex items-center justify-center">
                  <FaImage className="text-gray-400 text-4xl" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-12">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Royal Gallery</h2>
          <div className="max-w-md mx-auto bg-red-50 border border-red-200 rounded-lg p-6">
            <p className="text-red-600 mb-4">{error}</p>
            <button 
              onClick={handleRetry}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (images.length === 0) {
    return (
      <div className="py-12">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Royal Gallery</h2>
          <div className="max-w-md mx-auto bg-gray-50 border border-gray-200 rounded-lg p-6">
            <FaImage className="text-gray-400 text-5xl mx-auto mb-4" />
            <p className="text-gray-600 mb-4">No images found in the gallery.</p>
            <button 
              onClick={handleRetry}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Refresh
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <section id="royal-gallery" className={`py-12 md:py-16 relative overflow-hidden ${isHomepage ? 'bg-white' : 'bg-gray-50'}`}>
      <div className="container mx-auto px-4 relative z-10">
        {!isHomepage && (
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Royal Gallery
            </h2>
            <div className="w-20 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Explore the rich cultural heritage and royal traditions through our curated gallery.
            </p>
          </div>
        )}

        {/* Gallery Grid */}
        <div className="mb-16">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 max-w-4xl mx-auto">
            {carouselImages.slice(0, 6).map((image, index) => (
              <div
                key={`gallery-${index}-${image.title}`}
                className="group cursor-pointer"
                onClick={() => handleOpenModal(image, index)}
              >
                <div className="relative overflow-hidden rounded-xl border-2 border-royalGold/30 hover:border-royalGold transition-all duration-300 shadow-lg hover:shadow-xl">
                  <div className="aspect-square relative">
                    <Image
                      src={image.url}
                      alt={image.title}
                      width={300}
                      height={300}
                      className="w-full h-full object-cover transform transition-transform duration-300 group-hover:scale-105"
                      priority={index < 6}
                    />
                    {/* Golden overlay on hover */}
                    <div className="absolute inset-0 bg-gradient-to-t from-royalGold/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>


                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Image Modal */}
        {selectedImage && (
          <div 
            className="fixed inset-0 bg-black/95 z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedImage(null)}
          >
            <div 
              className="relative w-full max-w-5xl max-h-[90vh]"
              onClick={e => e.stopPropagation()}
            >
              <button 
                className="absolute -top-10 right-0 text-white hover:text-royalGold transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedImage(null);
                }}
              >
                <FaTimes className="text-2xl" />
              </button>
              <div className="bg-white rounded-lg overflow-hidden shadow-2xl">
                <div className="relative h-[70vh] bg-black">
                  <img 
                    src={selectedImage.url} 
                    alt={selectedImage.title || 'Gallery image'}
                    className="w-full h-full object-contain"
                  />
                  {images.length > 1 && (
                    <>
                      <button 
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate('prev');
                        }}
                        className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/50 text-white p-3 rounded-full hover:bg-black/70 transition-colors"
                      >
                        <FaChevronLeft />
                      </button>
                      <button 
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate('next');
                        }}
                        className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/50 text-white p-3 rounded-full hover:bg-black/70 transition-colors"
                      >
                        <FaChevronRight />
                      </button>
                    </>
                  )}
                </div>
                <div className="p-4 bg-white">
                  {selectedImage.title && (
                    <h3 className="text-lg font-semibold text-gray-900">
                      {selectedImage.title.includes(':') 
                        ? selectedImage.title.split(':').pop()?.trim() 
                        : selectedImage.title}
                    </h3>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {isHomepage && images.length > 0 && (
          <div className="text-center mt-16">
            <div className="bg-gradient-to-r from-royalBlue/5 to-royalGold/5 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-xl max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-royalBlue mb-4">Explore Our Gallery</h3>
              <p className="text-gray-700 mb-6 leading-relaxed">
                Discover the complete collection of royal images, historical documents, and cultural artifacts in our comprehensive digital archive.
              </p>
              <a
                href="/gallery"
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-royalGold to-yellow-500 text-royalBlue font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 transform"
              >
                <i className="fas fa-images mr-2"></i>
                View Full Gallery
              </a>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default RoyalGallery;
