'use client';

import React from 'react';

const UpliftGhana = () => {
  const corePillars = [
    {
      id: 1,
      title: 'Education for Empowerment',
      icon: '🎓',
      items: [
        'Renovation of 11 high schools across the Eastern Region',
        'Deployment of digital learning labs and teacher development programs',
        'Scholarships and leadership academies for underserved youth'
      ]
    },
    {
      id: 2,
      title: 'Entrepreneurship & Local Industry',
      icon: '💼',
      items: [
        'Microgrant programs for youth and women entrepreneurs',
        'Agro-processing and manufacturing cluster development',
        'Royal Business Incubator in Adukrom for startups and artisans'
      ]
    },
    {
      id: 3,
      title: 'Sustainable Infrastructure & Energy',
      icon: '⚡',
      items: [
        'Smart community development projects',
        'Expansion of solar-powered microgrids and eco-housing units',
        'Modernization of the Adukrom Business District as a model economic hub'
      ]
    },
    {
      id: 4,
      title: 'Cultural Renaissance & Diaspora Engagement',
      icon: '🎭',
      items: [
        'Cultural heritage preservation through digital archiving and festivals',
        'Diaspora "Return to Invest" campaign under royal leadership',
        'Arts and fashion collaborations showcasing Ghana\'s identity to the world'
      ]
    },
    {
      id: 5,
      title: 'Health & Human Services',
      icon: '🏥',
      items: [
        'Community clinics and maternal care initiatives',
        'Nutrition and clean water access in rural districts',
        'Royal Mobile Health Caravans for outreach'
      ]
    }
  ];

  const joinOptions = [
    {
      title: 'Become a Founding Partner',
      description: 'Join our network of strategic partners and help shape the future of Ghana.',
      icon: '🤝',
      action: 'Partner With Us'
    },
    {
      title: 'Sponsor a Project',
      description: 'Support specific initiatives aligned with your organization\'s values and goals.',
      icon: '💰',
      action: 'Sponsor Now'
    },
    {
      title: 'Engage Your Diaspora Network',
      description: 'Connect Ghanaians abroad with opportunities to contribute to national development.',
      icon: '🌍',
      action: 'Connect Network'
    },
    {
      title: 'Attend the Uplift Ghana Royal Summit 2025',
      description: 'Join leaders and changemakers at our inaugural summit to chart Ghana\'s future.',
      icon: '👑',
      action: 'Register Now'
    }
  ];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalNavy text-white">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="royal-pattern w-full h-full"></div>
        </div>

        <div className="max-w-7xl mx-auto text-center relative z-10">
          <h1 className="text-5xl md:text-7xl font-serif font-bold mb-4">Uplift Ghana</h1>
          <h2 className="text-2xl md:text-3xl font-serif text-royalGold mb-6">
            A Royal Initiative for National Prosperity, Inclusion, and Innovation
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-8"></div>
          <p className="text-xl text-gray-200 max-w-4xl mx-auto mb-8 leading-relaxed">
            A visionary initiative launched under the patronage of His Majesty Mpuntuhene Allen Ellison,
            designed to accelerate socio-economic transformation across Ghana through strategic investments
            in education, entrepreneurship, infrastructure, and cultural empowerment.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="#join"
              className="px-8 py-4 bg-royalGold text-royalBlue font-bold rounded-xl hover:bg-yellow-400 transition-colors duration-300 shadow-lg hover:shadow-xl"
            >
              Join the Movement
            </a>
            <a
              href="#pillars"
              className="px-8 py-4 bg-transparent border-2 border-white text-white font-bold rounded-xl hover:bg-white/10 transition-colors duration-300"
            >
              Our Pillars
            </a>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-royalBlue mb-4">Overview</h2>
            <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-8"></div>
            <p className="text-lg text-gray-700 max-w-5xl mx-auto leading-relaxed">
              Rooted in the values of unity, sustainability, and Pan-African excellence, Uplift Ghana is a call to action—uniting
              public and private sectors, diaspora leaders, global investors, and communities to build a thriving, future-ready Ghana.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12">
            {/* Mission */}
            <div className="bg-white p-8 rounded-2xl shadow-lg">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-royalBlue rounded-full flex items-center justify-center mr-4">
                  <span className="text-white text-xl">🎯</span>
                </div>
                <h3 className="text-2xl font-bold text-royalBlue">Mission Statement</h3>
              </div>
              <p className="text-gray-700 leading-relaxed">
                To empower communities across Ghana by promoting inclusive development, nurturing local talent,
                and mobilizing global partnerships that foster economic opportunity and elevate national pride.
              </p>
            </div>

            {/* Vision */}
            <div className="bg-white p-8 rounded-2xl shadow-lg">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-royalGold rounded-full flex items-center justify-center mr-4">
                  <span className="text-white text-xl">👁️</span>
                </div>
                <h3 className="text-2xl font-bold text-royalBlue">Vision Statement</h3>
              </div>
              <p className="text-gray-700 leading-relaxed">
                A Ghana where every citizen has the tools, resources, and platform to rise—economically, educationally,
                and culturally—under the banner of unity, innovation, and royal-led advancement.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Core Pillars */}
      <section id="pillars" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-royalBlue mb-4">Core Pillars of Uplift Ghana</h2>
            <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
            <p className="text-gray-600 max-w-3xl mx-auto">
              Five strategic pillars designed to transform Ghana through comprehensive development initiatives.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {corePillars.map((pillar) => (
              <div key={pillar.id} className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
                <div className="p-8">
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-royalBlue rounded-full flex items-center justify-center mr-4">
                      <span className="text-white text-xl">{pillar.icon}</span>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">{pillar.title}</h3>
                  </div>
                  <ul className="space-y-3">
                    {pillar.items.map((item, index) => (
                      <li key={index} className="flex items-start">
                        <div className="w-2 h-2 bg-royalGold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <span className="text-gray-700 text-sm leading-relaxed">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Leadership */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-royalBlue mb-4">Led By</h2>
            <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          </div>

          <div className="bg-white rounded-2xl shadow-xl p-8 md:p-12 text-center">
            <div className="mb-8">
              <h3 className="text-2xl md:text-3xl font-bold text-royalBlue mb-4">The Royal Palace of Adukrom</h3>
              <p className="text-xl text-royalGold font-semibold mb-6">
                Under the leadership of His Majesty Mpuntuhene Allen Ellison
              </p>
              <div className="w-16 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-8"></div>
              <p className="text-gray-700 leading-relaxed max-w-4xl mx-auto">
                In strategic alliance with the Ellison Royal Sovereign Wealth Fund, the Nifaman Council of the Akuapem State,
                and national and international development partners.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-6 mt-12">
              <div className="text-center">
                <div className="w-16 h-16 bg-royalBlue rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-2xl">👑</span>
                </div>
                <h4 className="font-bold text-gray-900">Royal Leadership</h4>
                <p className="text-sm text-gray-600">His Majesty Mpuntuhene Allen Ellison</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-royalGold rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-2xl">💰</span>
                </div>
                <h4 className="font-bold text-gray-900">Strategic Funding</h4>
                <p className="text-sm text-gray-600">Ellison Royal Sovereign Wealth Fund</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-royalBlue rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-2xl">🤝</span>
                </div>
                <h4 className="font-bold text-gray-900">Traditional Council</h4>
                <p className="text-sm text-gray-600">Nifaman Council of Akuapem State</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Join the Movement */}
      <section id="join" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-royalBlue mb-4">Join the Movement</h2>
            <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
            <p className="text-lg text-gray-700 max-w-4xl mx-auto leading-relaxed">
              Whether you're an investor, institution, policymaker, or proud Ghanaian, Uplift Ghana offers a platform to create lasting impact.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-16">
            {joinOptions.map((option) => (
              <div key={option.title} className="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-royalBlue rounded-full flex items-center justify-center mr-4">
                    <span className="text-white text-xl">{option.icon}</span>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900">{option.title}</h3>
                </div>
                <p className="text-gray-700 mb-6 leading-relaxed">{option.description}</p>
                <button className="w-full bg-royalGold text-white font-bold py-3 px-6 rounded-xl hover:bg-yellow-500 transition-colors duration-300">
                  {option.action}
                </button>
              </div>
            ))}
          </div>

          {/* Call to Action */}
          <div className="bg-gradient-to-r from-royalBlue to-royalNavy rounded-2xl p-8 md:p-12 text-center text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-6">Ready to Make an Impact?</h3>
            <p className="text-xl text-gray-200 mb-8 max-w-3xl mx-auto">
              Contact us to get involved and help shape the future of Ghana through the Uplift Ghana initiative.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/contact"
                className="px-8 py-4 bg-royalGold text-royalBlue font-bold rounded-xl hover:bg-yellow-400 transition-colors duration-300 shadow-lg hover:shadow-xl"
              >
                Contact Us to Get Involved
              </a>
              <a
                href="#pillars"
                className="px-8 py-4 bg-transparent border-2 border-white text-white font-bold rounded-xl hover:bg-white/10 transition-colors duration-300"
              >
                Learn More
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default UpliftGhana;
