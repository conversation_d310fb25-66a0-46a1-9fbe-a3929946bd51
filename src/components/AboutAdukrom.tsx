'use client'

const AboutAdukrom: React.FC = () => {
  const features = [
    {
      icon: 'fas fa-mountain',
      title: 'Elevated Location',
      description: 'The Togo Atakora Hills offer natural fortification, cooler climates, and stunning vistas — ideal for eco-tourism and royal ceremonies.',
      gradient: 'from-royalGold to-yellow-500'
    },
    {
      icon: 'fas fa-truck',
      title: 'Trade Connectivity',
      description: 'Sitting on the Ho-Koforidua main trunk road, Adukrom links major economic centers as a burgeoning hub for commerce.',
      gradient: 'from-royalBlue to-blue-600'
    },
    {
      icon: 'fas fa-seedling',
      title: 'Agricultural Potential',
      description: 'Fertile lands and favorable weather conditions make Adukrom a center for sustainable agriculture and agri-business.',
      gradient: 'from-forestGreen to-green-600'
    },
    {
      icon: 'fas fa-bolt',
      title: 'Renewable Energy',
      description: 'The landscape is primed for green energy initiatives, including wind and solar projects aligned with sustainability goals.',
      gradient: 'from-purple-600 to-purple-800'
    }
  ]

  return (
    <section id="about-adukrom" className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/20 via-royalGold/5 to-royalGold/15"></div>
      <div className="absolute inset-0 bg-gradient-to-bl from-transparent via-royalGold/3 to-royalGold/8"></div>
      
      {/* Animated background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="royal-pattern w-full h-full"></div>
      </div>

      {/* Floating elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-br from-royalGold/10 to-transparent rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-20 w-64 h-64 bg-gradient-to-tl from-white/10 to-transparent rounded-full blur-2xl" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-4">
            About Adukrom Kingdom
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-white/90 max-w-3xl mx-auto leading-relaxed">
            The Crown of Africa - A traditional royal kingdom where heritage meets innovation, 
            empowering our people through cultural preservation, economic development, and international diplomacy.
          </p>
        </div>

        {/* Main content grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
          {/* Left side - Kingdom overview */}
          <div className="space-y-6">
            <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl">
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 flex items-center justify-center mr-4">
                  <img
                    src="/Website Images/logo.png"
                    alt="Adukrom Kingdom Logo"
                    className="w-full h-full object-contain"
                  />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white">Adukrom Kingdom</h3>
                  <p className="text-royalGold">The Crown of Africa</p>
                </div>
              </div>
              
              <p className="text-white/90 leading-relaxed mb-4">
                Nestled atop the serene heights of the Togo Atakora Hills, Adukrom commands breathtaking views and an even more remarkable legacy. As the capital of the Okere District Assembly in Ghana's Eastern Region, Adukrom serves as a gateway between Ghana's vibrant eastern corridor and the broader West African trade routes.
              </p>

              <p className="text-white/90 leading-relaxed mb-6">
                Renowned as the seat of the revered Nifahene Stool of Akuapem, Adukrom is where history breathes through every pathway, and where the future is being shaped by visionary leadership under His Majesty Mpuntuhene Allen Ellison.
              </p>

              <a
                href="/about-adukrom"
                className="text-royalGold hover:text-yellow-300 font-semibold text-sm transition-colors duration-300 flex items-center mb-4"
              >
                <i className="fas fa-book-open mr-2"></i>
                Read More About Adukrom
              </a>

              <div className="grid grid-cols-2 gap-4">
                <div className="bg-white/10 rounded-xl p-4 text-center">
                  <div className="text-royalGold text-2xl mb-2">
                    <i className="fas fa-crown"></i>
                  </div>
                  <p className="text-white font-semibold">Nifahene</p>
                  <p className="text-white/70 text-sm">Stool of Akuapem</p>
                </div>
                <div className="bg-white/10 rounded-xl p-4 text-center">
                  <div className="text-royalGold text-2xl mb-2">
                    <i className="fas fa-map-marker-alt"></i>
                  </div>
                  <p className="text-white font-semibold">Eastern</p>
                  <p className="text-white/70 text-sm">Region Capital</p>
                </div>
              </div>
            </div>
          </div>

          {/* Right side - Vision statement */}
          <div>
            <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl h-full">
              {/* Freedom and Justice Image - same size as left box */}
              <div className="w-full h-full min-h-[300px] rounded-2xl overflow-hidden">
                <img
                  src="/Website Images/Freedom-and-justice-bright.png"
                  alt="Freedom and Justice - Adukrom Kingdom"
                  className="w-full h-full object-cover object-center"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Features grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300"
            >
              <div className={`w-14 h-14 bg-gradient-to-br ${feature.gradient} rounded-xl flex items-center justify-center mb-4 mx-auto`}>
                <i className={`${feature.icon} text-white text-xl`}></i>
              </div>
              <h4 className="text-lg font-bold text-white text-center mb-3">{feature.title}</h4>
              <p className="text-white/80 text-sm text-center leading-relaxed">{feature.description}</p>
            </div>
          ))}
        </div>

        {/* Historical Timeline */}
        <div className="mt-20">
          <div className="text-center mb-16">
            <h3 className="text-3xl md:text-4xl font-serif font-bold text-white mb-4">
              Kingdom Timeline
            </h3>
            <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
            <p className="text-lg text-white/90 max-w-3xl mx-auto leading-relaxed">
              A journey through the rich history of Adukrom Kingdom, from ancient traditions to modern leadership.
            </p>
          </div>

          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-royalGold via-royalGold/50 to-royalGold"></div>

            {/* Timeline events */}
            <div className="space-y-12">
              {/* Ancient Era */}
              <div className="relative flex items-center">
                <div className="w-1/2 pr-8 text-right">
                  <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 shadow-xl">
                    <h4 className="text-xl font-bold text-royalGold mb-2">Ancient Era</h4>
                    <p className="text-sm text-royalGold/80 mb-3">15th - 17th Century</p>
                    <p className="text-white/90 text-sm leading-relaxed">
                      Establishment of the Nifahene Stool of Akuapem. Traditional governance systems
                      and cultural practices take root in the Togo Atakora Hills.
                    </p>
                  </div>
                </div>
                <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-royalGold rounded-full border-4 border-white shadow-lg"></div>
                <div className="w-1/2 pl-8"></div>
              </div>

              {/* Colonial Period */}
              <div className="relative flex items-center">
                <div className="w-1/2 pr-8"></div>
                <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-royalGold rounded-full border-4 border-white shadow-lg"></div>
                <div className="w-1/2 pl-8">
                  <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 shadow-xl">
                    <h4 className="text-xl font-bold text-royalGold mb-2">Colonial Resistance</h4>
                    <p className="text-sm text-royalGold/80 mb-3">18th - 19th Century</p>
                    <p className="text-white/90 text-sm leading-relaxed">
                      Adukrom leaders maintain cultural autonomy and traditional governance
                      while navigating colonial pressures and protecting community interests.
                    </p>
                  </div>
                </div>
              </div>

              {/* Independence Era */}
              <div className="relative flex items-center">
                <div className="w-1/2 pr-8 text-right">
                  <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 shadow-xl">
                    <h4 className="text-xl font-bold text-royalGold mb-2">Independence & Growth</h4>
                    <p className="text-sm text-royalGold/80 mb-3">1957 - 2000</p>
                    <p className="text-white/90 text-sm leading-relaxed">
                      Ghana's independence brings new opportunities. Adukrom becomes a vital
                      center for regional development and cultural preservation.
                    </p>
                  </div>
                </div>
                <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-royalGold rounded-full border-4 border-white shadow-lg"></div>
                <div className="w-1/2 pl-8"></div>
              </div>

              {/* Modern Era */}
              <div className="relative flex items-center">
                <div className="w-1/2 pr-8"></div>
                <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-royalGold rounded-full border-4 border-white shadow-lg"></div>
                <div className="w-1/2 pl-8">
                  <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 shadow-xl">
                    <h4 className="text-xl font-bold text-royalGold mb-2">Modern Renaissance</h4>
                    <p className="text-sm text-royalGold/80 mb-3">2000 - 2024</p>
                    <p className="text-white/90 text-sm leading-relaxed">
                      Strategic development initiatives, international partnerships, and
                      sustainable growth projects position Adukrom as a model kingdom.
                    </p>
                  </div>
                </div>
              </div>

              {/* Current Era */}
              <div className="relative flex items-center">
                <div className="w-1/2 pr-8 text-right">
                  <div className="bg-gradient-to-br from-royalGold/20 to-royalGold/10 backdrop-blur-lg rounded-2xl p-6 border border-royalGold/30 shadow-xl">
                    <h4 className="text-xl font-bold text-royalGold mb-2">The Crown of Africa Era</h4>
                    <p className="text-sm text-royalGold/80 mb-3">2025 - Present</p>
                    <p className="text-white/90 text-sm leading-relaxed">
                      Under His Royal Majesty King Allen Ellison, Adukrom enters a new golden age
                      of innovation, global partnerships, and sustainable development.
                    </p>
                  </div>
                </div>
                <div className="absolute left-1/2 transform -translate-x-1/2 w-6 h-6 bg-gradient-to-br from-royalGold to-yellow-400 rounded-full border-4 border-white shadow-lg animate-pulse"></div>
                <div className="w-1/2 pl-8"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Call to action */}
        <div className="text-center mt-16">
          <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">Join Our Journey</h3>
            <p className="text-white/90 mb-6 leading-relaxed">
              Be part of a kingdom where tradition empowers transformation, and where every connection
              contributes to shaping Africa's prosperous future.
            </p>
            <a
              href="#contact"
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-royalGold to-yellow-500 text-royalBlue font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <i className="fas fa-handshake mr-2"></i>
              Connect With Us
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

export default AboutAdukrom
